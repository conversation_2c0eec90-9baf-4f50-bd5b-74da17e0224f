#!/usr/bin/env python3
"""
Test script to verify the debug functionality works correctly
"""

import json

# Sample unit details response (based on the curl example provided)
sample_unit_details = {
    "id": "68cbe5695b697f00079ac073",
    "city": "city",
    "region": "region",
    "model": "الأبراج",
    "buildingNumber": "71",
    "floor": "الأخير",
    "unitNumber": "57",
    "unitType": "unit",
    "unitCode": "08-71-57",
    "area": "97",
    "roomCount": "3",
    "finishing": "full",
    "pricePerMeter": "18,000",
    "unitRemainingReservationFeesUSD": 11760.73,
    "adminFeesUSD": "541",
    "excellencePercentages": "0%",
    "alternativeCompletion": "firstAlternativeCompletion50"
}

def format_unit_details(unit_details):
    """Format unit details for display (same logic as in the debug method)"""
    details_text = "تفاصيل الوحدة:\n\n"
    details_text += f"ID: {unit_details.get('id', 'غير متوفر')}\n"
    details_text += f"المدينة: {unit_details.get('city', 'غير متوفر')}\n"
    details_text += f"المنطقة: {unit_details.get('region', 'غير متوفر')}\n"
    details_text += f"النموذج: {unit_details.get('model', 'غير متوفر')}\n"
    details_text += f"رقم المبنى: {unit_details.get('buildingNumber', 'غير متوفر')}\n"
    details_text += f"الدور: {unit_details.get('floor', 'غير متوفر')}\n"
    details_text += f"رقم الوحدة: {unit_details.get('unitNumber', 'غير متوفر')}\n"
    details_text += f"نوع الوحدة: {unit_details.get('unitType', 'غير متوفر')}\n"
    details_text += f"كود الوحدة: {unit_details.get('unitCode', 'غير متوفر')}\n"
    details_text += f"المساحة: {unit_details.get('area', 'غير متوفر')} متر مربع\n"
    details_text += f"عدد الغرف: {unit_details.get('roomCount', 'غير متوفر')}\n"
    details_text += f"التشطيب: {unit_details.get('finishing', 'غير متوفر')}\n"
    details_text += f"سعر المتر: {unit_details.get('pricePerMeter', 'غير متوفر')} جنيه\n"
    details_text += f"رسوم الحجز المتبقية (دولار): {unit_details.get('unitRemainingReservationFeesUSD', 'غير متوفر')}\n"
    details_text += f"الرسوم الإدارية (دولار): {unit_details.get('adminFeesUSD', 'غير متوفر')}\n"
    details_text += f"نسبة التميز: {unit_details.get('excellencePercentages', 'غير متوفر')}\n"
    details_text += f"الاستكمال البديل: {unit_details.get('alternativeCompletion', 'غير متوفر')}\n"
    
    return details_text

def test_debug_formatting():
    """Test the debug formatting functionality"""
    print("Testing debug formatting...")
    
    formatted_details = format_unit_details(sample_unit_details)
    print("Formatted details:")
    print(formatted_details)
    
    print("\nRaw JSON:")
    print(json.dumps(sample_unit_details, ensure_ascii=False, indent=2))
    
    print("\n✅ Debug formatting test completed successfully!")

if __name__ == "__main__":
    test_debug_formatting()
