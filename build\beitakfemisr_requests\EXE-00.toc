('E:\\Programming\\Python\\Freelance\\Ahmed '
 '<PERSON>el<PERSON>by\\beitakfemisr\\dist\\beitakfemisr_bot.exe',
 True,
 False,
 False,
 'E:\\Programming\\Python\\Freelance\\Ahmed '
 'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-console.ico',
 None,
 False,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'E:\\Programming\\Python\\Freelance\\Ahmed '
 'Abdelnaby\\beitakfemisr\\build\\beitakfemisr_requests\\beitakfemisr_bot.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\build\\beitakfemisr_requests\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\build\\beitakfemisr_requests\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\build\\beitakfemisr_requests\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\build\\beitakfemisr_requests\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\build\\beitakfemisr_requests\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\build\\beitakfemisr_requests\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('beitakfemisr_requests',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\beitakfemisr_requests.py',
   'PYSOURCE'),
  ('python312.dll', 'C:\\Program Files\\Python312\\python312.dll', 'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('select.pyd', 'C:\\Program Files\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Program Files\\Python312\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'C:\\Program Files\\Python312\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program Files\\Python312\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'C:\\Program Files\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Program Files\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'C:\\Program Files\\Python312\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'C:\\Program Files\\Python312\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp312-win_amd64.pyd',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\sip.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files\\Python312\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Program Files\\Python312\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Program Files\\Python312\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program Files\\Python312\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Program Files\\Python312\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('python3.dll', 'C:\\Program Files\\Python312\\python3.dll', 'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('pyfiglet\\fonts\\1943____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\1943____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\1row.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\1row.flf',
   'DATA'),
  ('pyfiglet\\fonts\\3-d.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\3-d.flf',
   'DATA'),
  ('pyfiglet\\fonts\\3d-ascii.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\3d-ascii.flf',
   'DATA'),
  ('pyfiglet\\fonts\\3d_diagonal.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\3d_diagonal.flf',
   'DATA'),
  ('pyfiglet\\fonts\\3x5.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\3x5.flf',
   'DATA'),
  ('pyfiglet\\fonts\\4max.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\4max.flf',
   'DATA'),
  ('pyfiglet\\fonts\\4x4_offr.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\4x4_offr.flf',
   'DATA'),
  ('pyfiglet\\fonts\\5lineoblique.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\5lineoblique.flf',
   'DATA'),
  ('pyfiglet\\fonts\\5x7.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\5x7.flf',
   'DATA'),
  ('pyfiglet\\fonts\\5x8.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\5x8.flf',
   'DATA'),
  ('pyfiglet\\fonts\\64f1____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\64f1____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\6x10.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\6x10.flf',
   'DATA'),
  ('pyfiglet\\fonts\\6x9.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\6x9.flf',
   'DATA'),
  ('pyfiglet\\fonts\\__init__.py',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\__init__.py',
   'DATA'),
  ('pyfiglet\\fonts\\__pycache__\\__init__.cpython-312.pyc',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('pyfiglet\\fonts\\a_zooloo.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\a_zooloo.flf',
   'DATA'),
  ('pyfiglet\\fonts\\acrobatic.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\acrobatic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\advenger.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\advenger.flf',
   'DATA'),
  ('pyfiglet\\fonts\\alligator.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\alligator.flf',
   'DATA'),
  ('pyfiglet\\fonts\\alligator2.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\alligator2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\alpha.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\alpha.flf',
   'DATA'),
  ('pyfiglet\\fonts\\alphabet.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\alphabet.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_3_line.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_3_line.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_3_liv1.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_3_liv1.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_aaa01.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_aaa01.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_neko.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_neko.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_razor.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_razor.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_razor2.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_razor2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_slash.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_slash.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_slider.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_slider.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_thin.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_thin.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_tubes.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_tubes.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_untitled.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_untitled.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ansi_regular.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ansi_regular.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ansi_shadow.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ansi_shadow.flf',
   'DATA'),
  ('pyfiglet\\fonts\\aquaplan.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\aquaplan.flf',
   'DATA'),
  ('pyfiglet\\fonts\\arrows.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\arrows.flf',
   'DATA'),
  ('pyfiglet\\fonts\\asc_____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\asc_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ascii12.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ascii12.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\ascii9.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ascii9.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\ascii___.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ascii___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ascii_new_roman.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ascii_new_roman.flf',
   'DATA'),
  ('pyfiglet\\fonts\\assalt_m.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\assalt_m.flf',
   'DATA'),
  ('pyfiglet\\fonts\\asslt__m.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\asslt__m.flf',
   'DATA'),
  ('pyfiglet\\fonts\\atc_____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\atc_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\atc_gran.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\atc_gran.flf',
   'DATA'),
  ('pyfiglet\\fonts\\avatar.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\avatar.flf',
   'DATA'),
  ('pyfiglet\\fonts\\b1ff.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\b1ff.flf',
   'DATA'),
  ('pyfiglet\\fonts\\b_m__200.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\b_m__200.flf',
   'DATA'),
  ('pyfiglet\\fonts\\banner.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\banner.flf',
   'DATA'),
  ('pyfiglet\\fonts\\banner3-D.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\banner3-D.flf',
   'DATA'),
  ('pyfiglet\\fonts\\banner3.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\banner3.flf',
   'DATA'),
  ('pyfiglet\\fonts\\banner4.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\banner4.flf',
   'DATA'),
  ('pyfiglet\\fonts\\barbwire.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\barbwire.flf',
   'DATA'),
  ('pyfiglet\\fonts\\basic.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\basic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\battle_s.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\battle_s.flf',
   'DATA'),
  ('pyfiglet\\fonts\\battlesh.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\battlesh.flf',
   'DATA'),
  ('pyfiglet\\fonts\\baz__bil.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\baz__bil.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bear.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bear.flf',
   'DATA'),
  ('pyfiglet\\fonts\\beer_pub.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\beer_pub.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bell.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bell.flf',
   'DATA'),
  ('pyfiglet\\fonts\\benjamin.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\benjamin.flf',
   'DATA'),
  ('pyfiglet\\fonts\\big.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\big.flf',
   'DATA'),
  ('pyfiglet\\fonts\\big_money-ne.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\big_money-ne.flf',
   'DATA'),
  ('pyfiglet\\fonts\\big_money-nw.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\big_money-nw.flf',
   'DATA'),
  ('pyfiglet\\fonts\\big_money-se.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\big_money-se.flf',
   'DATA'),
  ('pyfiglet\\fonts\\big_money-sw.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\big_money-sw.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bigascii12.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bigascii12.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\bigascii9.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bigascii9.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\bigchief.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bigchief.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bigfig.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bigfig.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bigmono12.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bigmono12.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\bigmono9.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bigmono9.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\binary.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\binary.flf',
   'DATA'),
  ('pyfiglet\\fonts\\block.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\block.flf',
   'DATA'),
  ('pyfiglet\\fonts\\blocks.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\blocks.flf',
   'DATA'),
  ('pyfiglet\\fonts\\blocky.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\blocky.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bloody.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bloody.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bolger.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bolger.flf',
   'DATA'),
  ('pyfiglet\\fonts\\braced.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\braced.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bright.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bright.flf',
   'DATA'),
  ('pyfiglet\\fonts\\brite.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\brite.flf',
   'DATA'),
  ('pyfiglet\\fonts\\briteb.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\briteb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\britebi.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\britebi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\britei.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\britei.flf',
   'DATA'),
  ('pyfiglet\\fonts\\broadway.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\broadway.flf',
   'DATA'),
  ('pyfiglet\\fonts\\broadway_kb.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\broadway_kb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bubble.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bubble.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bubble__.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bubble__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bubble_b.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bubble_b.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bulbhead.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bulbhead.flf',
   'DATA'),
  ('pyfiglet\\fonts\\c1______.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\c1______.flf',
   'DATA'),
  ('pyfiglet\\fonts\\c2______.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\c2______.flf',
   'DATA'),
  ('pyfiglet\\fonts\\c_ascii_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\c_ascii_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\c_consen.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\c_consen.flf',
   'DATA'),
  ('pyfiglet\\fonts\\calgphy2.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\calgphy2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\caligraphy.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\caligraphy.flf',
   'DATA'),
  ('pyfiglet\\fonts\\calvin_s.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\calvin_s.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cards.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cards.flf',
   'DATA'),
  ('pyfiglet\\fonts\\catwalk.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\catwalk.flf',
   'DATA'),
  ('pyfiglet\\fonts\\caus_in_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\caus_in_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\char1___.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\char1___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\char2___.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\char2___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\char3___.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\char3___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\char4___.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\char4___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\charact1.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\charact1.flf',
   'DATA'),
  ('pyfiglet\\fonts\\charact2.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\charact2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\charact3.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\charact3.flf',
   'DATA'),
  ('pyfiglet\\fonts\\charact4.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\charact4.flf',
   'DATA'),
  ('pyfiglet\\fonts\\charact5.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\charact5.flf',
   'DATA'),
  ('pyfiglet\\fonts\\charact6.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\charact6.flf',
   'DATA'),
  ('pyfiglet\\fonts\\characte.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\characte.flf',
   'DATA'),
  ('pyfiglet\\fonts\\charset_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\charset_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\chartr.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\chartr.flf',
   'DATA'),
  ('pyfiglet\\fonts\\chartri.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\chartri.flf',
   'DATA'),
  ('pyfiglet\\fonts\\chiseled.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\chiseled.flf',
   'DATA'),
  ('pyfiglet\\fonts\\chunky.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\chunky.flf',
   'DATA'),
  ('pyfiglet\\fonts\\circle.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\circle.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\clb6x10.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clb6x10.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clb8x10.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clb8x10.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clb8x8.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clb8x8.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cli8x8.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cli8x8.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr4x6.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr4x6.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr5x10.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr5x10.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr5x6.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr5x6.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr5x8.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr5x8.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr6x10.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr6x10.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr6x6.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr6x6.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr6x8.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr6x8.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr7x10.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr7x10.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr7x8.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr7x8.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr8x10.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr8x10.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr8x8.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr8x8.flf',
   'DATA'),
  ('pyfiglet\\fonts\\coil_cop.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\coil_cop.flf',
   'DATA'),
  ('pyfiglet\\fonts\\coinstak.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\coinstak.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cola.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cola.flf',
   'DATA'),
  ('pyfiglet\\fonts\\colossal.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\colossal.flf',
   'DATA'),
  ('pyfiglet\\fonts\\com_sen_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\com_sen_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\computer.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\computer.flf',
   'DATA'),
  ('pyfiglet\\fonts\\contessa.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\contessa.flf',
   'DATA'),
  ('pyfiglet\\fonts\\contrast.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\contrast.flf',
   'DATA'),
  ('pyfiglet\\fonts\\convoy__.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\convoy__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cosmic.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cosmic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cosmike.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cosmike.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cour.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cour.flf',
   'DATA'),
  ('pyfiglet\\fonts\\courb.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\courb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\courbi.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\courbi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\couri.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\couri.flf',
   'DATA'),
  ('pyfiglet\\fonts\\crawford.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\crawford.flf',
   'DATA'),
  ('pyfiglet\\fonts\\crawford2.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\crawford2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\crazy.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\crazy.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cricket.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cricket.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cursive.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cursive.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cyberlarge.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cyberlarge.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cybermedium.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cybermedium.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cybersmall.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cybersmall.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cygnet.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cygnet.flf',
   'DATA'),
  ('pyfiglet\\fonts\\d_dragon.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\d_dragon.flf',
   'DATA'),
  ('pyfiglet\\fonts\\danc4.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\danc4.flf',
   'DATA'),
  ('pyfiglet\\fonts\\dancing_font.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\dancing_font.flf',
   'DATA'),
  ('pyfiglet\\fonts\\dcs_bfmo.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\dcs_bfmo.flf',
   'DATA'),
  ('pyfiglet\\fonts\\decimal.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\decimal.flf',
   'DATA'),
  ('pyfiglet\\fonts\\deep_str.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\deep_str.flf',
   'DATA'),
  ('pyfiglet\\fonts\\def_leppard.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\def_leppard.flf',
   'DATA'),
  ('pyfiglet\\fonts\\defleppard.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\defleppard.flf',
   'DATA'),
  ('pyfiglet\\fonts\\delta_corps_priest_1.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\delta_corps_priest_1.flf',
   'DATA'),
  ('pyfiglet\\fonts\\demo_1__.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\demo_1__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\demo_2__.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\demo_2__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\demo_m__.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\demo_m__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\devilish.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\devilish.flf',
   'DATA'),
  ('pyfiglet\\fonts\\diamond.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\diamond.flf',
   'DATA'),
  ('pyfiglet\\fonts\\diet_cola.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\diet_cola.flf',
   'DATA'),
  ('pyfiglet\\fonts\\digital.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\digital.flf',
   'DATA'),
  ('pyfiglet\\fonts\\doh.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\doh.flf',
   'DATA'),
  ('pyfiglet\\fonts\\doom.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\doom.flf',
   'DATA'),
  ('pyfiglet\\fonts\\dos_rebel.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\dos_rebel.flf',
   'DATA'),
  ('pyfiglet\\fonts\\dotmatrix.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\dotmatrix.flf',
   'DATA'),
  ('pyfiglet\\fonts\\double.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\double.flf',
   'DATA'),
  ('pyfiglet\\fonts\\double_blocky.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\double_blocky.flf',
   'DATA'),
  ('pyfiglet\\fonts\\double_shorts.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\double_shorts.flf',
   'DATA'),
  ('pyfiglet\\fonts\\drpepper.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\drpepper.flf',
   'DATA'),
  ('pyfiglet\\fonts\\druid___.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\druid___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\dwhistled.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\dwhistled.flf',
   'DATA'),
  ('pyfiglet\\fonts\\e__fist_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\e__fist_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ebbs_1__.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ebbs_1__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ebbs_2__.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ebbs_2__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\eca_____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\eca_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\efti_robot.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\efti_robot.flf',
   'DATA'),
  ('pyfiglet\\fonts\\eftichess.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\eftichess.flf',
   'DATA'),
  ('pyfiglet\\fonts\\eftifont.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\eftifont.flf',
   'DATA'),
  ('pyfiglet\\fonts\\eftipiti.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\eftipiti.flf',
   'DATA'),
  ('pyfiglet\\fonts\\eftirobot.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\eftirobot.flf',
   'DATA'),
  ('pyfiglet\\fonts\\eftitalic.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\eftitalic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\eftiwall.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\eftiwall.flf',
   'DATA'),
  ('pyfiglet\\fonts\\eftiwater.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\eftiwater.flf',
   'DATA'),
  ('pyfiglet\\fonts\\electronic.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\electronic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\elite.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\elite.flf',
   'DATA'),
  ('pyfiglet\\fonts\\emboss.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\emboss.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\emboss2.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\emboss2.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\epic.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\epic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\etcrvs__.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\etcrvs__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\f15_____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\f15_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\faces_of.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\faces_of.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fair_mea.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fair_mea.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fairligh.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fairligh.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fantasy_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fantasy_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fbr12___.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fbr12___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fbr1____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fbr1____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fbr2____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fbr2____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fbr_stri.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fbr_stri.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fbr_tilt.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fbr_tilt.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fender.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fender.flf',
   'DATA'),
  ('pyfiglet\\fonts\\filter.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\filter.flf',
   'DATA'),
  ('pyfiglet\\fonts\\finalass.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\finalass.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fire_font-k.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fire_font-k.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fire_font-s.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fire_font-s.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fireing_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fireing_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\flipped.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\flipped.flf',
   'DATA'),
  ('pyfiglet\\fonts\\flower_power.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\flower_power.flf',
   'DATA'),
  ('pyfiglet\\fonts\\flyn_sh.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\flyn_sh.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fourtops.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fourtops.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fp1_____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fp1_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fp2_____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fp2_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fraktur.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fraktur.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fun_face.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fun_face.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fun_faces.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fun_faces.flf',
   'DATA'),
  ('pyfiglet\\fonts\\funky_dr.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\funky_dr.flf',
   'DATA'),
  ('pyfiglet\\fonts\\future.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\future.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\future_1.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\future_1.flf',
   'DATA'),
  ('pyfiglet\\fonts\\future_2.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\future_2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\future_3.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\future_3.flf',
   'DATA'),
  ('pyfiglet\\fonts\\future_4.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\future_4.flf',
   'DATA'),
  ('pyfiglet\\fonts\\future_5.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\future_5.flf',
   'DATA'),
  ('pyfiglet\\fonts\\future_6.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\future_6.flf',
   'DATA'),
  ('pyfiglet\\fonts\\future_7.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\future_7.flf',
   'DATA'),
  ('pyfiglet\\fonts\\future_8.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\future_8.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fuzzy.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fuzzy.flf',
   'DATA'),
  ('pyfiglet\\fonts\\gauntlet.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\gauntlet.flf',
   'DATA'),
  ('pyfiglet\\fonts\\georgi16.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\georgi16.flf',
   'DATA'),
  ('pyfiglet\\fonts\\georgia11.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\georgia11.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ghost.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ghost.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ghost_bo.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ghost_bo.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ghoulish.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ghoulish.flf',
   'DATA'),
  ('pyfiglet\\fonts\\glenyn.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\glenyn.flf',
   'DATA'),
  ('pyfiglet\\fonts\\goofy.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\goofy.flf',
   'DATA'),
  ('pyfiglet\\fonts\\gothic.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\gothic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\gothic__.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\gothic__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\graceful.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\graceful.flf',
   'DATA'),
  ('pyfiglet\\fonts\\gradient.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\gradient.flf',
   'DATA'),
  ('pyfiglet\\fonts\\graffiti.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\graffiti.flf',
   'DATA'),
  ('pyfiglet\\fonts\\grand_pr.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\grand_pr.flf',
   'DATA'),
  ('pyfiglet\\fonts\\greek.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\greek.flf',
   'DATA'),
  ('pyfiglet\\fonts\\green_be.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\green_be.flf',
   'DATA'),
  ('pyfiglet\\fonts\\hades___.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\hades___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\heart_left.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\heart_left.flf',
   'DATA'),
  ('pyfiglet\\fonts\\heart_right.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\heart_right.flf',
   'DATA'),
  ('pyfiglet\\fonts\\heavy_me.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\heavy_me.flf',
   'DATA'),
  ('pyfiglet\\fonts\\helv.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\helv.flf',
   'DATA'),
  ('pyfiglet\\fonts\\helvb.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\helvb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\helvbi.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\helvbi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\helvi.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\helvi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\henry_3d.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\henry_3d.flf',
   'DATA'),
  ('pyfiglet\\fonts\\heroboti.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\heroboti.flf',
   'DATA'),
  ('pyfiglet\\fonts\\hex.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\hex.flf',
   'DATA'),
  ('pyfiglet\\fonts\\hieroglyphs.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\hieroglyphs.flf',
   'DATA'),
  ('pyfiglet\\fonts\\high_noo.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\high_noo.flf',
   'DATA'),
  ('pyfiglet\\fonts\\hills___.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\hills___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\hollywood.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\hollywood.flf',
   'DATA'),
  ('pyfiglet\\fonts\\home_pak.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\home_pak.flf',
   'DATA'),
  ('pyfiglet\\fonts\\horizontal_left.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\horizontal_left.flf',
   'DATA'),
  ('pyfiglet\\fonts\\horizontal_right.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\horizontal_right.flf',
   'DATA'),
  ('pyfiglet\\fonts\\house_of.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\house_of.flf',
   'DATA'),
  ('pyfiglet\\fonts\\hypa_bal.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\hypa_bal.flf',
   'DATA'),
  ('pyfiglet\\fonts\\hyper___.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\hyper___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\icl-1900.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\icl-1900.flf',
   'DATA'),
  ('pyfiglet\\fonts\\impossible.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\impossible.flf',
   'DATA'),
  ('pyfiglet\\fonts\\inc_raw_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\inc_raw_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\invita.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\invita.flf',
   'DATA'),
  ('pyfiglet\\fonts\\isometric1.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\isometric1.flf',
   'DATA'),
  ('pyfiglet\\fonts\\isometric2.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\isometric2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\isometric3.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\isometric3.flf',
   'DATA'),
  ('pyfiglet\\fonts\\isometric4.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\isometric4.flf',
   'DATA'),
  ('pyfiglet\\fonts\\italic.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\italic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\italics_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\italics_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ivrit.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ivrit.flf',
   'DATA'),
  ('pyfiglet\\fonts\\jacky.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\jacky.flf',
   'DATA'),
  ('pyfiglet\\fonts\\jazmine.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\jazmine.flf',
   'DATA'),
  ('pyfiglet\\fonts\\jerusalem.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\jerusalem.flf',
   'DATA'),
  ('pyfiglet\\fonts\\joust___.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\joust___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\js_block_letters.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\js_block_letters.flf',
   'DATA'),
  ('pyfiglet\\fonts\\js_bracket_letters.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\js_bracket_letters.flf',
   'DATA'),
  ('pyfiglet\\fonts\\js_capital_curves.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\js_capital_curves.flf',
   'DATA'),
  ('pyfiglet\\fonts\\js_cursive.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\js_cursive.flf',
   'DATA'),
  ('pyfiglet\\fonts\\js_stick_letters.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\js_stick_letters.flf',
   'DATA'),
  ('pyfiglet\\fonts\\katakana.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\katakana.flf',
   'DATA'),
  ('pyfiglet\\fonts\\kban.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\kban.flf',
   'DATA'),
  ('pyfiglet\\fonts\\keyboard.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\keyboard.flf',
   'DATA'),
  ('pyfiglet\\fonts\\kgames_i.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\kgames_i.flf',
   'DATA'),
  ('pyfiglet\\fonts\\kik_star.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\kik_star.flf',
   'DATA'),
  ('pyfiglet\\fonts\\knob.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\knob.flf',
   'DATA'),
  ('pyfiglet\\fonts\\konto.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\konto.flf',
   'DATA'),
  ('pyfiglet\\fonts\\konto_slant.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\konto_slant.flf',
   'DATA'),
  ('pyfiglet\\fonts\\krak_out.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\krak_out.flf',
   'DATA'),
  ('pyfiglet\\fonts\\larry3d.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\larry3d.flf',
   'DATA'),
  ('pyfiglet\\fonts\\lazy_jon.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\lazy_jon.flf',
   'DATA'),
  ('pyfiglet\\fonts\\lcd.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\lcd.flf',
   'DATA'),
  ('pyfiglet\\fonts\\lean.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\lean.flf',
   'DATA'),
  ('pyfiglet\\fonts\\letter.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\letter.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\letter_w.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\letter_w.flf',
   'DATA'),
  ('pyfiglet\\fonts\\letters.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\letters.flf',
   'DATA'),
  ('pyfiglet\\fonts\\letterw3.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\letterw3.flf',
   'DATA'),
  ('pyfiglet\\fonts\\lexible_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\lexible_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\lil_devil.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\lil_devil.flf',
   'DATA'),
  ('pyfiglet\\fonts\\line_blocks.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\line_blocks.flf',
   'DATA'),
  ('pyfiglet\\fonts\\linux.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\linux.flf',
   'DATA'),
  ('pyfiglet\\fonts\\lockergnome.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\lockergnome.flf',
   'DATA'),
  ('pyfiglet\\fonts\\lower.flc',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\lower.flc',
   'DATA'),
  ('pyfiglet\\fonts\\mad_nurs.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mad_nurs.flf',
   'DATA'),
  ('pyfiglet\\fonts\\madrid.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\madrid.flf',
   'DATA'),
  ('pyfiglet\\fonts\\magic_ma.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\magic_ma.flf',
   'DATA'),
  ('pyfiglet\\fonts\\marquee.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\marquee.flf',
   'DATA'),
  ('pyfiglet\\fonts\\master_o.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\master_o.flf',
   'DATA'),
  ('pyfiglet\\fonts\\maxfour.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\maxfour.flf',
   'DATA'),
  ('pyfiglet\\fonts\\mayhem_d.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mayhem_d.flf',
   'DATA'),
  ('pyfiglet\\fonts\\mcg_____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mcg_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\merlin1.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\merlin1.flf',
   'DATA'),
  ('pyfiglet\\fonts\\merlin2.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\merlin2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\mig_ally.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mig_ally.flf',
   'DATA'),
  ('pyfiglet\\fonts\\mike.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mike.flf',
   'DATA'),
  ('pyfiglet\\fonts\\mini.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mini.flf',
   'DATA'),
  ('pyfiglet\\fonts\\mirror.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mirror.flf',
   'DATA'),
  ('pyfiglet\\fonts\\mnemonic.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mnemonic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\modern__.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\modern__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\modular.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\modular.flf',
   'DATA'),
  ('pyfiglet\\fonts\\mono12.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mono12.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\mono9.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mono9.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\morse.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\morse.flf',
   'DATA'),
  ('pyfiglet\\fonts\\morse2.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\morse2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\moscow.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\moscow.flf',
   'DATA'),
  ('pyfiglet\\fonts\\mshebrew210.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mshebrew210.flf',
   'DATA'),
  ('pyfiglet\\fonts\\muzzle.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\muzzle.flf',
   'DATA'),
  ('pyfiglet\\fonts\\nancyj-fancy.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\nancyj-fancy.flf',
   'DATA'),
  ('pyfiglet\\fonts\\nancyj-improved.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\nancyj-improved.flf',
   'DATA'),
  ('pyfiglet\\fonts\\nancyj-underlined.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\nancyj-underlined.flf',
   'DATA'),
  ('pyfiglet\\fonts\\nancyj.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\nancyj.flf',
   'DATA'),
  ('pyfiglet\\fonts\\new_asci.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\new_asci.flf',
   'DATA'),
  ('pyfiglet\\fonts\\nfi1____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\nfi1____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\nipples.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\nipples.flf',
   'DATA'),
  ('pyfiglet\\fonts\\notie_ca.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\notie_ca.flf',
   'DATA'),
  ('pyfiglet\\fonts\\npn_____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\npn_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\nscript.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\nscript.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ntgreek.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ntgreek.flf',
   'DATA'),
  ('pyfiglet\\fonts\\null.flc',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\null.flc',
   'DATA'),
  ('pyfiglet\\fonts\\nvscript.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\nvscript.flf',
   'DATA'),
  ('pyfiglet\\fonts\\o8.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\o8.flf',
   'DATA'),
  ('pyfiglet\\fonts\\octal.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\octal.flf',
   'DATA'),
  ('pyfiglet\\fonts\\odel_lak.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\odel_lak.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ogre.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ogre.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ok_beer_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ok_beer_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\old_banner.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\old_banner.flf',
   'DATA'),
  ('pyfiglet\\fonts\\os2.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\os2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\outrun__.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\outrun__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\p_s_h_m_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\p_s_h_m_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\p_skateb.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\p_skateb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\pacos_pe.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\pacos_pe.flf',
   'DATA'),
  ('pyfiglet\\fonts\\pagga.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\pagga.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\panther_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\panther_.flf',
   'DATA'),
  ("pyfiglet\\fonts\\patorjk's_cheese.flf",
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   "Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\patorjk's_cheese.flf",
   'DATA'),
  ('pyfiglet\\fonts\\patorjk-hex.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\patorjk-hex.flf',
   'DATA'),
  ('pyfiglet\\fonts\\pawn_ins.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\pawn_ins.flf',
   'DATA'),
  ('pyfiglet\\fonts\\pawp.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\pawp.flf',
   'DATA'),
  ('pyfiglet\\fonts\\peaks.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\peaks.flf',
   'DATA'),
  ('pyfiglet\\fonts\\pebbles.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\pebbles.flf',
   'DATA'),
  ('pyfiglet\\fonts\\pepper.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\pepper.flf',
   'DATA'),
  ('pyfiglet\\fonts\\phonix__.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\phonix__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\platoon2.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\platoon2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\platoon_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\platoon_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\pod_____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\pod_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\poison.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\poison.flf',
   'DATA'),
  ('pyfiglet\\fonts\\puffy.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\puffy.flf',
   'DATA'),
  ('pyfiglet\\fonts\\puzzle.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\puzzle.flf',
   'DATA'),
  ('pyfiglet\\fonts\\pyramid.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\pyramid.flf',
   'DATA'),
  ('pyfiglet\\fonts\\r2-d2___.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\r2-d2___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rad_____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rad_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rad_phan.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rad_phan.flf',
   'DATA'),
  ('pyfiglet\\fonts\\radical_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\radical_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rainbow_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rainbow_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rally_s2.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rally_s2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rally_sp.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rally_sp.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rammstein.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rammstein.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rampage_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rampage_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rastan__.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rastan__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\raw_recu.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\raw_recu.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rci_____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rci_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rectangles.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rectangles.flf',
   'DATA'),
  ('pyfiglet\\fonts\\red_phoenix.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\red_phoenix.flf',
   'DATA'),
  ('pyfiglet\\fonts\\relief.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\relief.flf',
   'DATA'),
  ('pyfiglet\\fonts\\relief2.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\relief2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rev.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rev.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ripper!_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ripper!_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\road_rai.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\road_rai.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rockbox_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rockbox_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rok_____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rok_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\roman.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\roman.flf',
   'DATA'),
  ('pyfiglet\\fonts\\roman___.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\roman___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rot13.flc',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rot13.flc',
   'DATA'),
  ('pyfiglet\\fonts\\rot13.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rot13.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rotated.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rotated.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rounded.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rounded.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rowancap.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rowancap.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rozzo.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rozzo.flf',
   'DATA'),
  ('pyfiglet\\fonts\\runic.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\runic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\runyc.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\runyc.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sans.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sans.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sansb.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sansb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sansbi.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sansbi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sansi.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sansi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\santa_clara.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\santa_clara.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sblood.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sblood.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sbook.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sbook.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sbookb.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sbookb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sbookbi.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sbookbi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sbooki.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sbooki.flf',
   'DATA'),
  ('pyfiglet\\fonts\\script.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\script.flf',
   'DATA'),
  ('pyfiglet\\fonts\\script__.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\script__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\serifcap.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\serifcap.flf',
   'DATA'),
  ('pyfiglet\\fonts\\shadow.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\shadow.flf',
   'DATA'),
  ('pyfiglet\\fonts\\shimrod.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\shimrod.flf',
   'DATA'),
  ('pyfiglet\\fonts\\short.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\short.flf',
   'DATA'),
  ('pyfiglet\\fonts\\skate_ro.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\skate_ro.flf',
   'DATA'),
  ('pyfiglet\\fonts\\skateord.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\skateord.flf',
   'DATA'),
  ('pyfiglet\\fonts\\skateroc.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\skateroc.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sketch_s.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sketch_s.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sl_script.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sl_script.flf',
   'DATA'),
  ('pyfiglet\\fonts\\slant.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\slant.flf',
   'DATA'),
  ('pyfiglet\\fonts\\slant_relief.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\slant_relief.flf',
   'DATA'),
  ('pyfiglet\\fonts\\slide.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\slide.flf',
   'DATA'),
  ('pyfiglet\\fonts\\slscript.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\slscript.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sm______.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sm______.flf',
   'DATA'),
  ('pyfiglet\\fonts\\small.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\small.flf',
   'DATA'),
  ('pyfiglet\\fonts\\small_caps.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\small_caps.flf',
   'DATA'),
  ('pyfiglet\\fonts\\small_poison.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\small_poison.flf',
   'DATA'),
  ('pyfiglet\\fonts\\small_shadow.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\small_shadow.flf',
   'DATA'),
  ('pyfiglet\\fonts\\small_slant.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\small_slant.flf',
   'DATA'),
  ('pyfiglet\\fonts\\smascii12.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smascii12.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\smascii9.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smascii9.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\smblock.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smblock.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\smbraille.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smbraille.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\smisome1.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smisome1.flf',
   'DATA'),
  ('pyfiglet\\fonts\\smkeyboard.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smkeyboard.flf',
   'DATA'),
  ('pyfiglet\\fonts\\smmono12.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smmono12.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\smmono9.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smmono9.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\smscript.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smscript.flf',
   'DATA'),
  ('pyfiglet\\fonts\\smshadow.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smshadow.flf',
   'DATA'),
  ('pyfiglet\\fonts\\smslant.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smslant.flf',
   'DATA'),
  ('pyfiglet\\fonts\\smtengwar.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smtengwar.flf',
   'DATA'),
  ('pyfiglet\\fonts\\soft.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\soft.flf',
   'DATA'),
  ('pyfiglet\\fonts\\space_op.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\space_op.flf',
   'DATA'),
  ('pyfiglet\\fonts\\spc_demo.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\spc_demo.flf',
   'DATA'),
  ('pyfiglet\\fonts\\speed.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\speed.flf',
   'DATA'),
  ('pyfiglet\\fonts\\spliff.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\spliff.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stacey.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stacey.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stampate.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stampate.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stampatello.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stampatello.flf',
   'DATA'),
  ('pyfiglet\\fonts\\standard.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\standard.flf',
   'DATA'),
  ('pyfiglet\\fonts\\star_strips.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\star_strips.flf',
   'DATA'),
  ('pyfiglet\\fonts\\star_war.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\star_war.flf',
   'DATA'),
  ('pyfiglet\\fonts\\starwars.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\starwars.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stealth_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stealth_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stellar.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stellar.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stencil1.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stencil1.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stencil2.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stencil2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stforek.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stforek.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stick_letters.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stick_letters.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stop.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stop.flf',
   'DATA'),
  ('pyfiglet\\fonts\\straight.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\straight.flf',
   'DATA'),
  ('pyfiglet\\fonts\\street_s.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\street_s.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stronger_than_all.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stronger_than_all.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sub-zero.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sub-zero.flf',
   'DATA'),
  ('pyfiglet\\fonts\\subteran.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\subteran.flf',
   'DATA'),
  ('pyfiglet\\fonts\\super_te.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\super_te.flf',
   'DATA'),
  ('pyfiglet\\fonts\\swamp_land.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\swamp_land.flf',
   'DATA'),
  ('pyfiglet\\fonts\\swan.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\swan.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sweet.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sweet.flf',
   'DATA'),
  ('pyfiglet\\fonts\\t__of_ap.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\t__of_ap.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tanja.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tanja.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tav1____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tav1____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\taxi____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\taxi____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tec1____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tec1____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tec_7000.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tec_7000.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tecrvs__.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tecrvs__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tengwar.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tengwar.flf',
   'DATA'),
  ('pyfiglet\\fonts\\term.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\term.flf',
   'DATA'),
  ('pyfiglet\\fonts\\test1.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\test1.flf',
   'DATA'),
  ('pyfiglet\\fonts\\the_edge.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\the_edge.flf',
   'DATA'),
  ('pyfiglet\\fonts\\thick.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\thick.flf',
   'DATA'),
  ('pyfiglet\\fonts\\thin.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\thin.flf',
   'DATA'),
  ('pyfiglet\\fonts\\this.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\this.flf',
   'DATA'),
  ('pyfiglet\\fonts\\thorned.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\thorned.flf',
   'DATA'),
  ('pyfiglet\\fonts\\threepoint.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\threepoint.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ti_pan__.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ti_pan__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ticks.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ticks.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ticksslant.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ticksslant.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tiles.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tiles.flf',
   'DATA'),
  ('pyfiglet\\fonts\\times.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\times.flf',
   'DATA'),
  ('pyfiglet\\fonts\\timesofl.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\timesofl.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tinker-toy.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tinker-toy.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tomahawk.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tomahawk.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tombstone.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tombstone.flf',
   'DATA'),
  ('pyfiglet\\fonts\\top_duck.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\top_duck.flf',
   'DATA'),
  ('pyfiglet\\fonts\\train.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\train.flf',
   'DATA'),
  ('pyfiglet\\fonts\\trashman.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\trashman.flf',
   'DATA'),
  ('pyfiglet\\fonts\\trek.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\trek.flf',
   'DATA'),
  ('pyfiglet\\fonts\\triad_st.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\triad_st.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ts1_____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ts1_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tsalagi.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tsalagi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tsm_____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tsm_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tsn_base.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tsn_base.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tty.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tty.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ttyb.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ttyb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tubular.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tubular.flf',
   'DATA'),
  ('pyfiglet\\fonts\\twin_cob.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\twin_cob.flf',
   'DATA'),
  ('pyfiglet\\fonts\\twisted.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\twisted.flf',
   'DATA'),
  ('pyfiglet\\fonts\\twopoint.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\twopoint.flf',
   'DATA'),
  ('pyfiglet\\fonts\\type_set.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\type_set.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ucf_fan_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ucf_fan_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ugalympi.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ugalympi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\unarmed_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\unarmed_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\univers.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\univers.flf',
   'DATA'),
  ('pyfiglet\\fonts\\upper.flc',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\upper.flc',
   'DATA'),
  ('pyfiglet\\fonts\\usa_____.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\usa_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\usa_pq__.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\usa_pq__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\usaflag.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\usaflag.flf',
   'DATA'),
  ('pyfiglet\\fonts\\utopia.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\utopia.flf',
   'DATA'),
  ('pyfiglet\\fonts\\utopiab.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\utopiab.flf',
   'DATA'),
  ('pyfiglet\\fonts\\utopiabi.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\utopiabi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\utopiai.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\utopiai.flf',
   'DATA'),
  ('pyfiglet\\fonts\\varsity.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\varsity.flf',
   'DATA'),
  ('pyfiglet\\fonts\\vortron_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\vortron_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\war_of_w.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\war_of_w.flf',
   'DATA'),
  ('pyfiglet\\fonts\\wavy.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\wavy.flf',
   'DATA'),
  ('pyfiglet\\fonts\\weird.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\weird.flf',
   'DATA'),
  ('pyfiglet\\fonts\\wet_letter.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\wet_letter.flf',
   'DATA'),
  ('pyfiglet\\fonts\\whimsy.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\whimsy.flf',
   'DATA'),
  ('pyfiglet\\fonts\\wideterm.tlf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\wideterm.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\wow.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\wow.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xbrite.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xbrite.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xbriteb.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xbriteb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xbritebi.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xbritebi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xbritei.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xbritei.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xchartr.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xchartr.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xchartri.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xchartri.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xcour.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xcour.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xcourb.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xcourb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xcourbi.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xcourbi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xcouri.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xcouri.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xhelv.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xhelv.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xhelvb.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xhelvb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xhelvbi.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xhelvbi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xhelvi.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xhelvi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xsans.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xsans.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xsansb.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xsansb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xsansbi.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xsansbi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xsansi.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xsansi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xsbook.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xsbook.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xsbookb.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xsbookb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xsbookbi.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xsbookbi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xsbooki.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xsbooki.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xtimes.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xtimes.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xtty.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xtty.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xttyb.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xttyb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\yie-ar__.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\yie-ar__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\yie_ar_k.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\yie_ar_k.flf',
   'DATA'),
  ('pyfiglet\\fonts\\z-pilot_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\z-pilot_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\zig_zag_.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\zig_zag_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\zone7___.flf',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\zone7___.flf',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi\\cacert.pem',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('base_library.zip',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\build\\beitakfemisr_requests\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 **********,
 [('run.exe',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\run.exe',
   'EXECUTABLE')],
 'C:\\Program Files\\Python312\\python312.dll')
