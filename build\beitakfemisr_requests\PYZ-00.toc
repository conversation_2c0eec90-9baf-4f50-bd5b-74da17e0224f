('E:\\Programming\\Python\\Freelance\\Ahmed '
 'Abdelnaby\\beitakfemisr\\build\\beitakfemisr_requests\\PYZ-00.pyz',
 [('PyQt5',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Program Files\\Python312\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Program Files\\Python312\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program Files\\Python312\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Program Files\\Python312\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Program Files\\Python312\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime',
   'C:\\Program Files\\Python312\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Program Files\\Python312\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Program Files\\Python312\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime', 'C:\\Program Files\\Python312\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Program Files\\Python312\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'C:\\Program Files\\Python312\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Program Files\\Python312\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'C:\\Program Files\\Python312\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'C:\\Program Files\\Python312\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\Program Files\\Python312\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Program Files\\Python312\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('colorama',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Program Files\\Python312\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Program Files\\Python312\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Program Files\\Python312\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'C:\\Program Files\\Python312\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Program Files\\Python312\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'C:\\Program Files\\Python312\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program Files\\Python312\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Program Files\\Python312\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Program Files\\Python312\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'C:\\Program Files\\Python312\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\Program Files\\Python312\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'C:\\Program Files\\Python312\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\Program Files\\Python312\\Lib\\dis.py', 'PYMODULE'),
  ('elitesoftworks',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\elitesoftworks.py',
   'PYMODULE'),
  ('email',
   'C:\\Program Files\\Python312\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program Files\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program Files\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program Files\\Python312\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program Files\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program Files\\Python312\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program Files\\Python312\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program Files\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program Files\\Python312\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program Files\\Python312\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program Files\\Python312\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program Files\\Python312\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program Files\\Python312\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program Files\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program Files\\Python312\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program Files\\Python312\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program Files\\Python312\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program Files\\Python312\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program Files\\Python312\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program Files\\Python312\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Program Files\\Python312\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Program Files\\Python312\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Program Files\\Python312\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Program Files\\Python312\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Program Files\\Python312\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Program Files\\Python312\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Program Files\\Python312\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\Program Files\\Python312\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Program Files\\Python312\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Program Files\\Python312\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\Program Files\\Python312\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\Program Files\\Python312\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'C:\\Program Files\\Python312\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'C:\\Program Files\\Python312\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Program Files\\Python312\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Program Files\\Python312\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Program Files\\Python312\\Lib\\http\\server.py',
   'PYMODULE'),
  ('hwid', 'C:\\Program Files\\Python312\\Lib\\hwid.py', 'PYMODULE'),
  ('idna',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Program Files\\Python312\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Program Files\\Python312\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program Files\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program Files\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program Files\\Python312\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program Files\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Program Files\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program Files\\Python312\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Program Files\\Python312\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Program Files\\Python312\\Lib\\ipaddress.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json', 'C:\\Program Files\\Python312\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'C:\\Program Files\\Python312\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Program Files\\Python312\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Program Files\\Python312\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Program Files\\Python312\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'C:\\Program Files\\Python312\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Program Files\\Python312\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\Program Files\\Python312\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'C:\\Program Files\\Python312\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers', 'C:\\Program Files\\Python312\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'C:\\Program Files\\Python312\\Lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'C:\\Program Files\\Python312\\Lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Program Files\\Python312\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'C:\\Program Files\\Python312\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Program Files\\Python312\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Program Files\\Python312\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'C:\\Program Files\\Python312\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile',
   'C:\\Program Files\\Python312\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc', 'C:\\Program Files\\Python312\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'C:\\Program Files\\Python312\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Program Files\\Python312\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyfiglet',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\__init__.py',
   'PYMODULE'),
  ('pyfiglet.version',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\pyfiglet\\version.py',
   'PYMODULE'),
  ('queue', 'C:\\Program Files\\Python312\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Program Files\\Python312\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Program Files\\Python312\\Lib\\random.py', 'PYMODULE'),
  ('requests',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Program Files\\Python312\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'C:\\Program Files\\Python312\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\Program Files\\Python312\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Program Files\\Python312\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'C:\\Program Files\\Python312\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Program Files\\Python312\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Program Files\\Python312\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'C:\\Program Files\\Python312\\Lib\\site.py', 'PYMODULE'),
  ('socket', 'C:\\Program Files\\Python312\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'C:\\Program Files\\Python312\\Lib\\socketserver.py',
   'PYMODULE'),
  ('ssl', 'C:\\Program Files\\Python312\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'C:\\Program Files\\Python312\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'C:\\Program Files\\Python312\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'C:\\Program Files\\Python312\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Program Files\\Python312\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig', 'C:\\Program Files\\Python312\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'C:\\Program Files\\Python312\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Program Files\\Python312\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Program Files\\Python312\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Program Files\\Python312\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'C:\\Program Files\\Python312\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Program Files\\Python312\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib',
   'C:\\Program Files\\Python312\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\Program Files\\Python312\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\Program Files\\Python312\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\Program Files\\Python312\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Program Files\\Python312\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'C:\\Program Files\\Python312\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'C:\\Program Files\\Python312\\Lib\\typing.py', 'PYMODULE'),
  ('unittest',
   'C:\\Program Files\\Python312\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Program Files\\Python312\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Program Files\\Python312\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Program Files\\Python312\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Program Files\\Python312\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Program Files\\Python312\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Program Files\\Python312\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Program Files\\Python312\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Program Files\\Python312\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Program Files\\Python312\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Program Files\\Python312\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Program Files\\Python312\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Program Files\\Python312\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Program Files\\Python312\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program Files\\Python312\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Program Files\\Python312\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Program Files\\Python312\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'E:\\Programming\\Python\\Freelance\\Ahmed '
   'Abdelnaby\\beitakfemisr\\venv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Program Files\\Python312\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('xml', 'C:\\Program Files\\Python312\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers',
   'C:\\Program Files\\Python312\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Program Files\\Python312\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Program Files\\Python312\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Program Files\\Python312\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Program Files\\Python312\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile.__main__',
   'C:\\Program Files\\Python312\\Lib\\zipfile\\__main__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Program Files\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Program Files\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'C:\\Program Files\\Python312\\Lib\\zipimport.py', 'PYMODULE')])
