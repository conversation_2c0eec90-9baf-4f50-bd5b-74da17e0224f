[2025-10-05 21:05:25] جاري تسجيل الدخول...
[2025-10-05 21:05:26] ✅ تم تسجيل الدخول بنجاح
[2025-10-05 21:05:27] جاري تحميل الخدمات...
[2025-10-05 21:05:28] تم اختيار الخدمة: bookunitII
[2025-10-05 21:05:28] ❌ خطأ في تحميل خيارات الاستكمال: 0
[2025-10-05 21:05:29] ✅ تم تحميل 1 خدمة
[2025-10-05 21:09:57] جاري تسجيل الدخول...
[2025-10-05 21:09:57] ✅ تم تسجيل الدخول بنجاح
[2025-10-05 21:09:57] جاري تحميل الخدمات...
[2025-10-05 21:09:58] تم اختيار الخدمة: bookunit
[2025-10-05 21:09:59] ❌ خطأ في تحميل خيارات الاستكمال: 0
[2025-10-05 21:10:00] ✅ تم تحميل 34 خدمة
[2025-10-05 21:10:16] تم اختيار الخدمة: DIARNANEWCAIRO
[2025-10-05 21:10:16] ❌ خطأ في تحميل خيارات الاستكمال: 0
[2025-10-06 07:59:21] جاري تسجيل الدخول...
[2025-10-06 07:59:22] ✅ تم تسجيل الدخول بنجاح
[2025-10-06 07:59:22] جاري تحميل الخدمات...
[2025-10-06 07:59:23] تم اختيار الخدمة: bookunit
[2025-10-06 07:59:23] ❌ خطأ في تحميل خيارات الاستكمال: 0
[2025-10-06 07:59:25] ✅ تم تحميل 34 خدمة
[2025-10-06 07:59:26] تم اختيار الخدمة: NEWGARDENCITY
[2025-10-06 08:55:00] جاري تسجيل الدخول...
[2025-10-06 08:55:01] ✅ تم تسجيل الدخول بنجاح
[2025-10-06 08:55:01] جاري تحميل الخدمات...
[2025-10-06 08:55:02] استجابة API للخدمات: 200
[2025-10-06 08:55:02] تم استلام بيانات الخدمات: 4 فئة
[2025-10-06 08:55:02] 🔄 تم اختيار الخدمة: بيتك فى مصر (bookunit)
[2025-10-06 08:55:02] جاري تحميل خيارات الاستكمال للخدمة: bookunit
[2025-10-06 08:55:02] استجابة API للاستكمال: 200
[2025-10-06 08:55:02] تم استلام البيانات: 12 عنصر
[2025-10-06 08:55:02] ❌ خطأ في تحميل خيارات الاستكمال: 0
[2025-10-06 08:55:02] تفاصيل الخطأ: Traceback (most recent call last):
  File "E:\Programming\Python\Freelance\Ahmed Abdelnaby\beitakfemisr\beitakfemisr_requests.py", line 636, in load_alternative_completions
    if len(data[0]) == 0:
           ~~~~^^^
KeyError: 0

[2025-10-06 08:55:02] جاري تحميل خيارات model...
[2025-10-06 08:55:02] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-06 08:55:02] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 08:55:03] استجابة API لـ model: 422
[2025-10-06 08:55:03] ❌ فشل تحميل خيارات model: كود الاستجابة 422
[2025-10-06 08:55:03] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': '0092d250-a279-11f0-98e9-8db009ca6fab'}
[2025-10-06 08:55:03] جاري تحميل خيارات reservationRequest...
[2025-10-06 08:55:03] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-06 08:55:03] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 08:55:03] استجابة API لـ reservationRequest: 422
[2025-10-06 08:55:03] ❌ فشل تحميل خيارات reservationRequest: كود الاستجابة 422
[2025-10-06 08:55:03] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': '00ca5d10-a279-11f0-b1ce-993b7e417393'}
[2025-10-06 08:55:03] ✅ تم تحميل 34 خدمة
[2025-10-06 08:55:08] 🔄 تم اختيار الخدمة: مدينتي (MADINATY)
[2025-10-06 08:55:08] جاري تحميل خيارات الاستكمال للخدمة: MADINATY
[2025-10-06 08:55:08] استجابة API للاستكمال: 400
[2025-10-06 08:55:08] ❌ فشل تحميل خيارات الاستكمال: كود الاستجابة 400
[2025-10-06 08:55:08] تفاصيل الخطأ: {'code': 'INTEGRATION_ERROR', 'message': 'Integration error', 'details': [{'code': 'ASSERTION_ERROR', 'message': 'لا توجد طلبات جدية حجز تسمح لك التقديم على هذا المشروع', 'details': []}], 'statusCode': 400, 'supportId': '03c8a8f0-a279-11f0-8d7e-c529e274de3f'}
[2025-10-06 08:55:08] جاري تحميل خيارات model...
[2025-10-06 08:55:08] إرسال طلب إلى: https://api.beitakfemisr.com/api/MADINATY/fieldHandler
[2025-10-06 08:55:08] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 08:55:09] استجابة API لـ model: 200
[2025-10-06 08:55:09] تم استلام 0 خيار لـ model
[2025-10-06 08:55:09] ❌ لا يوجد combo box لـ model
[2025-10-06 08:55:09] جاري تحميل خيارات reservationRequest...
[2025-10-06 08:55:09] إرسال طلب إلى: https://api.beitakfemisr.com/api/MADINATY/fieldHandler
[2025-10-06 08:55:09] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 08:55:09] استجابة API لـ reservationRequest: 400
[2025-10-06 08:55:09] ❌ فشل تحميل خيارات reservationRequest: كود الاستجابة 400
[2025-10-06 08:55:09] تفاصيل الخطأ: {'code': 'INTEGRATION_ERROR', 'message': 'Integration error', 'details': [{'code': 'VM_ERROR', 'message': 'response.map is not a function', 'details': []}], 'statusCode': 400, 'supportId': '0442e200-a279-11f0-8021-4723b9a4b0f1'}
[2025-10-06 08:55:15] 🔄 تم اختيار الخدمة: ديارنا - بدر (DIARNABADR)
[2025-10-06 08:55:15] جاري تحميل خيارات الاستكمال للخدمة: DIARNABADR
[2025-10-06 08:55:15] استجابة API للاستكمال: 200
[2025-10-06 08:55:15] تم استلام البيانات: 11 عنصر
[2025-10-06 08:55:15] ❌ خطأ في تحميل خيارات الاستكمال: 0
[2025-10-06 08:55:15] تفاصيل الخطأ: Traceback (most recent call last):
  File "E:\Programming\Python\Freelance\Ahmed Abdelnaby\beitakfemisr\beitakfemisr_requests.py", line 636, in load_alternative_completions
    if len(data[0]) == 0:
           ~~~~^^^
KeyError: 0

[2025-10-06 08:55:15] جاري تحميل خيارات model...
[2025-10-06 08:55:15] إرسال طلب إلى: https://api.beitakfemisr.com/api/DIARNABADR/fieldHandler
[2025-10-06 08:55:15] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 08:55:16] استجابة API لـ model: 200
[2025-10-06 08:55:16] تم استلام 1 خيار لـ model
[2025-10-06 08:55:16] ❌ لا يوجد combo box لـ model
[2025-10-06 08:55:16] جاري تحميل خيارات reservationRequest...
[2025-10-06 08:55:16] إرسال طلب إلى: https://api.beitakfemisr.com/api/DIARNABADR/fieldHandler
[2025-10-06 08:55:16] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 08:55:16] استجابة API لـ reservationRequest: 200
[2025-10-06 08:55:16] تم استلام 1 خيار لـ reservationRequest
[2025-10-06 08:55:16] ❌ لا يوجد combo box لـ reservationRequest
[2025-10-06 08:59:32] جاري تسجيل الدخول...
[2025-10-06 08:59:32] ✅ تم تسجيل الدخول بنجاح
[2025-10-06 08:59:32] جاري تحميل الخدمات...
[2025-10-06 08:59:33] استجابة API للخدمات: 200
[2025-10-06 08:59:33] تم استلام بيانات الخدمات: 4 فئة
[2025-10-06 08:59:33] 🔄 تم اختيار الخدمة: بيتك فى مصر (bookunit)
[2025-10-06 08:59:33] جاري تحميل خيارات الاستكمال للخدمة: bookunit
[2025-10-06 08:59:33] استجابة API للاستكمال: 200
[2025-10-06 08:59:33] تم استلام البيانات: 12 عنصر
[2025-10-06 08:59:33] ❌ خطأ في تحميل خيارات الاستكمال: 0
[2025-10-06 08:59:33] تفاصيل الخطأ: Traceback (most recent call last):
  File "E:\Programming\Python\Freelance\Ahmed Abdelnaby\beitakfemisr\beitakfemisr_requests.py", line 636, in load_alternative_completions
    if len(data[0]) == 0:
           ~~~~^^^
KeyError: 0

[2025-10-06 08:59:33] جاري تحميل خيارات model...
[2025-10-06 08:59:33] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-06 08:59:33] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 08:59:34] استجابة API لـ model: 422
[2025-10-06 08:59:34] ❌ فشل تحميل خيارات model: كود الاستجابة 422
[2025-10-06 08:59:34] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': 'a20feaa0-a279-11f0-8275-8947fc1500a7'}
[2025-10-06 08:59:34] جاري تحميل خيارات reservationRequest...
[2025-10-06 08:59:34] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-06 08:59:34] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 08:59:34] استجابة API لـ reservationRequest: 422
[2025-10-06 08:59:34] ❌ فشل تحميل خيارات reservationRequest: كود الاستجابة 422
[2025-10-06 08:59:34] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': 'a24ea150-a279-11f0-9ea7-817ded3874a8'}
[2025-10-06 08:59:34] ✅ تم تحميل 34 خدمة
[2025-10-06 08:59:37] 🔄 تم اختيار الخدمة: فالي تاورز (VALLEYTOWERS)
[2025-10-06 08:59:37] جاري تحميل خيارات الاستكمال للخدمة: VALLEYTOWERS
[2025-10-06 08:59:37] استجابة API للاستكمال: 400
[2025-10-06 08:59:37] ❌ فشل تحميل خيارات الاستكمال: كود الاستجابة 400
[2025-10-06 08:59:37] تفاصيل الخطأ: {'code': 'INTEGRATION_ERROR', 'message': 'Integration error', 'details': [{'code': 'ASSERTION_ERROR', 'message': 'لا توجد طلبات جدية حجز تسمح لك التقديم على هذا المشروع', 'details': []}], 'statusCode': 400, 'supportId': 'a3fc0330-a279-11f0-85b8-1d8eb9905a94'}
[2025-10-06 08:59:37] جاري تحميل خيارات model...
[2025-10-06 08:59:37] إرسال طلب إلى: https://api.beitakfemisr.com/api/VALLEYTOWERS/fieldHandler
[2025-10-06 08:59:37] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 08:59:38] استجابة API لـ model: 200
[2025-10-06 08:59:38] تم استلام 1 خيار لـ model
[2025-10-06 08:59:38] Checking combo boxes:
[2025-10-06 08:59:38]   model_combo: True - <PyQt5.QtWidgets.QComboBox object at 0x0000028F3F950C20>
[2025-10-06 08:59:38]   building_combo: True - <PyQt5.QtWidgets.QComboBox object at 0x0000028F3F950CB0>
[2025-10-06 08:59:38]   floor_combo: True - <PyQt5.QtWidgets.QComboBox object at 0x0000028F3F950D40>
[2025-10-06 08:59:38]   unit_combo: True - <PyQt5.QtWidgets.QComboBox object at 0x0000028F3F9511C0>
[2025-10-06 08:59:38]   reservation_combo: True - <PyQt5.QtWidgets.QComboBox object at 0x0000028F3F9512E0>
[2025-10-06 08:59:38] البحث عن combo box لـ model
[2025-10-06 08:59:38] combo_map keys: ['model', 'buildingNumber', 'floor', 'unitNumber', 'reservationRequest']
[2025-10-06 08:59:38] combo object for model: <PyQt5.QtWidgets.QComboBox object at 0x0000028F3F950C20>
[2025-10-06 08:59:38] ❌ لا يوجد combo box لـ model
[2025-10-06 08:59:38] جاري تحميل خيارات reservationRequest...
[2025-10-06 08:59:38] إرسال طلب إلى: https://api.beitakfemisr.com/api/VALLEYTOWERS/fieldHandler
[2025-10-06 08:59:38] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 08:59:38] استجابة API لـ reservationRequest: 400
[2025-10-06 08:59:38] ❌ فشل تحميل خيارات reservationRequest: كود الاستجابة 400
[2025-10-06 08:59:38] تفاصيل الخطأ: {'code': 'INTEGRATION_ERROR', 'message': 'Integration error', 'details': [{'code': 'VM_ERROR', 'message': 'response.map is not a function', 'details': []}], 'statusCode': 400, 'supportId': 'a4964760-a279-11f0-b026-950da8e3a58d'}
[2025-10-06 09:00:26] جاري تسجيل الدخول...
[2025-10-06 09:00:27] ❌ فشل تسجيل الدخول: 502
[2025-10-06 09:00:37] جاري تسجيل الدخول...
[2025-10-06 09:00:37] ✅ تم تسجيل الدخول بنجاح
[2025-10-06 09:00:37] جاري تحميل الخدمات...
[2025-10-06 09:00:38] استجابة API للخدمات: 200
[2025-10-06 09:00:38] تم استلام بيانات الخدمات: 4 فئة
[2025-10-06 09:00:38] 🔄 تم اختيار الخدمة: بيتك فى مصر (bookunit)
[2025-10-06 09:00:38] جاري تحميل خيارات الاستكمال للخدمة: bookunit
[2025-10-06 09:00:38] استجابة API للاستكمال: 200
[2025-10-06 09:00:38] تم استلام البيانات: 12 عنصر
[2025-10-06 09:00:38] ❌ خطأ في تحميل خيارات الاستكمال: 0
[2025-10-06 09:00:38] تفاصيل الخطأ: Traceback (most recent call last):
  File "E:\Programming\Python\Freelance\Ahmed Abdelnaby\beitakfemisr\beitakfemisr_requests.py", line 636, in load_alternative_completions
    if len(data[0]) == 0:
           ~~~~^^^
KeyError: 0

[2025-10-06 09:00:38] جاري تحميل خيارات model...
[2025-10-06 09:00:38] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-06 09:00:38] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:00:40] استجابة API لـ model: 422
[2025-10-06 09:00:40] ❌ فشل تحميل خيارات model: كود الاستجابة 422
[2025-10-06 09:00:40] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': 'c97d25d0-a279-11f0-919d-1dce0bede080'}
[2025-10-06 09:00:40] جاري تحميل خيارات reservationRequest...
[2025-10-06 09:00:40] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-06 09:00:40] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:01:14] جاري تسجيل الدخول...
[2025-10-06 09:01:15] ✅ تم تسجيل الدخول بنجاح
[2025-10-06 09:01:15] جاري تحميل الخدمات...
[2025-10-06 09:01:15] استجابة API للخدمات: 200
[2025-10-06 09:01:15] تم استلام بيانات الخدمات: 4 فئة
[2025-10-06 09:01:15] 🔄 تم اختيار الخدمة: بيتك فى مصر (bookunit)
[2025-10-06 09:01:15] جاري تحميل خيارات الاستكمال للخدمة: bookunit
[2025-10-06 09:01:16] استجابة API للاستكمال: 200
[2025-10-06 09:01:16] تم استلام البيانات: 12 عنصر
[2025-10-06 09:01:16] ❌ خطأ في تحميل خيارات الاستكمال: 0
[2025-10-06 09:01:16] تفاصيل الخطأ: Traceback (most recent call last):
  File "E:\Programming\Python\Freelance\Ahmed Abdelnaby\beitakfemisr\beitakfemisr_requests.py", line 636, in load_alternative_completions
    if len(data[0]) == 0:
           ~~~~^^^
KeyError: 0

[2025-10-06 09:01:16] جاري تحميل خيارات model...
[2025-10-06 09:01:16] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-06 09:01:16] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:01:17] استجابة API لـ model: 422
[2025-10-06 09:01:17] ❌ فشل تحميل خيارات model: كود الاستجابة 422
[2025-10-06 09:01:17] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': 'df6a3ef0-a279-11f0-9e82-fd76000db19f'}
[2025-10-06 09:01:17] جاري تحميل خيارات reservationRequest...
[2025-10-06 09:01:17] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-06 09:01:17] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:01:17] استجابة API لـ reservationRequest: 422
[2025-10-06 09:01:17] ❌ فشل تحميل خيارات reservationRequest: كود الاستجابة 422
[2025-10-06 09:01:17] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': 'dfa6d2c0-a279-11f0-aa82-e9dfafee14f0'}
[2025-10-06 09:01:17] ✅ تم تحميل 34 خدمة
[2025-10-06 09:01:19] 🔄 تم اختيار الخدمة: مدينتي (MADINATY)
[2025-10-06 09:01:19] جاري تحميل خيارات الاستكمال للخدمة: MADINATY
[2025-10-06 09:01:19] استجابة API للاستكمال: 400
[2025-10-06 09:01:19] ❌ فشل تحميل خيارات الاستكمال: كود الاستجابة 400
[2025-10-06 09:01:19] تفاصيل الخطأ: {'code': 'INTEGRATION_ERROR', 'message': 'Integration error', 'details': [{'code': 'ASSERTION_ERROR', 'message': 'لا توجد طلبات جدية حجز تسمح لك التقديم على هذا المشروع', 'details': []}], 'statusCode': 400, 'supportId': 'e0c82140-a279-11f0-874a-21549da114a0'}
[2025-10-06 09:01:19] جاري تحميل خيارات model...
[2025-10-06 09:01:19] إرسال طلب إلى: https://api.beitakfemisr.com/api/MADINATY/fieldHandler
[2025-10-06 09:01:19] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:01:20] استجابة API لـ model: 200
[2025-10-06 09:01:20] تم استلام 0 خيار لـ model
[2025-10-06 09:01:20] combo for model: <PyQt5.QtWidgets.QComboBox object at 0x0000019761950D40> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-06 09:01:20] ✅ تم تحميل 0 خيار لـ model
[2025-10-06 09:01:20] جاري تحميل خيارات reservationRequest...
[2025-10-06 09:01:20] إرسال طلب إلى: https://api.beitakfemisr.com/api/MADINATY/fieldHandler
[2025-10-06 09:01:20] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:01:20] استجابة API لـ reservationRequest: 400
[2025-10-06 09:01:20] ❌ فشل تحميل خيارات reservationRequest: كود الاستجابة 400
[2025-10-06 09:01:20] تفاصيل الخطأ: {'code': 'INTEGRATION_ERROR', 'message': 'Integration error', 'details': [{'code': 'VM_ERROR', 'message': 'response.map is not a function', 'details': []}], 'statusCode': 400, 'supportId': 'e1709640-a279-11f0-aa82-e9dfafee14f0'}
[2025-10-06 09:03:49] جاري تسجيل الدخول...
[2025-10-06 09:03:49] ✅ تم تسجيل الدخول بنجاح
[2025-10-06 09:03:50] جاري تحميل الخدمات...
[2025-10-06 09:03:50] استجابة API للخدمات: 200
[2025-10-06 09:03:50] تم استلام بيانات الخدمات: 4 فئة
[2025-10-06 09:03:50] 🔄 تم اختيار الخدمة: بيتك فى مصر (bookunit)
[2025-10-06 09:03:50] جاري تحميل خيارات الاستكمال للخدمة: bookunit
[2025-10-06 09:03:51] استجابة API للاستكمال: 200
[2025-10-06 09:03:51] تم استلام البيانات: 12 عنصر
[2025-10-06 09:03:51] ❌ خطأ في تحميل خيارات الاستكمال: 0
[2025-10-06 09:03:51] تفاصيل الخطأ: Traceback (most recent call last):
  File "E:\Programming\Python\Freelance\Ahmed Abdelnaby\beitakfemisr\beitakfemisr_requests.py", line 636, in load_alternative_completions
    if len(data[0]) == 0:
           ~~~~^^^
KeyError: 0

[2025-10-06 09:03:51] جاري تحميل خيارات model...
[2025-10-06 09:03:51] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-06 09:03:51] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:03:52] استجابة API لـ model: 422
[2025-10-06 09:03:52] ❌ فشل تحميل خيارات model: كود الاستجابة 422
[2025-10-06 09:03:52] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': '3bd88200-a27a-11f0-874a-21549da114a0'}
[2025-10-06 09:03:52] جاري تحميل خيارات reservationRequest...
[2025-10-06 09:03:52] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-06 09:03:52] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:03:53] استجابة API لـ reservationRequest: 422
[2025-10-06 09:03:53] ❌ فشل تحميل خيارات reservationRequest: كود الاستجابة 422
[2025-10-06 09:03:53] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': '3c6112f0-a27a-11f0-9f3e-a3244edd5374'}
[2025-10-06 09:03:53] ✅ تم تحميل 34 خدمة
[2025-10-06 09:04:16] 🔄 تم اختيار الخدمة: ديارنا - القاهرة الجديدة (DIARNANEWCAIRO)
[2025-10-06 09:04:16] جاري تحميل خيارات الاستكمال للخدمة: DIARNANEWCAIRO
[2025-10-06 09:04:17] استجابة API للاستكمال: 200
[2025-10-06 09:04:17] تم استلام البيانات: 11 عنصر
[2025-10-06 09:04:17] ❌ خطأ في تحميل خيارات الاستكمال: 0
[2025-10-06 09:04:17] تفاصيل الخطأ: Traceback (most recent call last):
  File "E:\Programming\Python\Freelance\Ahmed Abdelnaby\beitakfemisr\beitakfemisr_requests.py", line 636, in load_alternative_completions
    if len(data[0]) == 0:
           ~~~~^^^
KeyError: 0

[2025-10-06 09:04:17] جاري تحميل خيارات model...
[2025-10-06 09:04:17] إرسال طلب إلى: https://api.beitakfemisr.com/api/DIARNANEWCAIRO/fieldHandler
[2025-10-06 09:04:17] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:04:17] استجابة API لـ model: 200
[2025-10-06 09:04:17] تم استلام 0 خيار لـ model
[2025-10-06 09:04:17] combo for model: <PyQt5.QtWidgets.QComboBox object at 0x000001B69ECB8B00> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-06 09:04:17] ✅ تم تحميل 0 خيار لـ model
[2025-10-06 09:04:17] جاري تحميل خيارات reservationRequest...
[2025-10-06 09:04:17] إرسال طلب إلى: https://api.beitakfemisr.com/api/DIARNANEWCAIRO/fieldHandler
[2025-10-06 09:04:17] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:04:18] استجابة API لـ reservationRequest: 200
[2025-10-06 09:04:18] تم استلام 1 خيار لـ reservationRequest
[2025-10-06 09:04:18] combo for reservationRequest: <PyQt5.QtWidgets.QComboBox object at 0x000001B69ECB91C0> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-06 09:04:18] ❌ خطأ في تحميل خيارات reservationRequest: arguments did not match any overloaded call:
  addItem(self, text: str, userData: Any = None): argument 1 has unexpected type 'int'
  addItem(self, icon: QIcon, text: str, userData: Any = None): argument 1 has unexpected type 'int'
[2025-10-06 09:04:18] تفاصيل الخطأ: Traceback (most recent call last):
  File "E:\Programming\Python\Freelance\Ahmed Abdelnaby\beitakfemisr\beitakfemisr_requests.py", line 770, in load_field_options
    combo.addItem(option['label'], option['value'])
TypeError: arguments did not match any overloaded call:
  addItem(self, text: str, userData: Any = None): argument 1 has unexpected type 'int'
  addItem(self, icon: QIcon, text: str, userData: Any = None): argument 1 has unexpected type 'int'

[2025-10-06 09:05:17] 🔄 تم اختيار الخدمة: ديارنا - دمياط الجديدة (DIARNANEWDAMIETTA)
[2025-10-06 09:05:17] جاري تحميل خيارات الاستكمال للخدمة: DIARNANEWDAMIETTA
[2025-10-06 09:05:18] استجابة API للاستكمال: 403
[2025-10-06 09:05:18] ❌ فشل تحميل خيارات الاستكمال: كود الاستجابة 403
[2025-10-06 09:05:18] تفاصيل الخطأ: {'code': 'UNAUTHORIZED', 'message': '', 'details': [], 'statusCode': 403, 'supportId': '6f3576d0-a27a-11f0-919d-1dce0bede080'}
[2025-10-06 09:05:18] جاري تحميل خيارات model...
[2025-10-06 09:05:38] إرسال طلب إلى: https://api.beitakfemisr.com/api/DIARNANEWDAMIETTA/fieldHandler
[2025-10-06 09:05:46] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:06:32] استجابة API لـ model: 401
[2025-10-06 09:06:32] ❌ فشل تحميل خيارات model: كود الاستجابة 401
[2025-10-06 09:06:32] تفاصيل الخطأ: {'code': 'AUTHORIZATION_REQUIRED', 'message': 'Authorization Required', 'details': [], 'statusCode': 401, 'supportId': '818237b0-a27a-11f0-acb4-e3bb29027242'}
[2025-10-06 09:06:32] جاري تحميل خيارات reservationRequest...
[2025-10-06 09:06:32] إرسال طلب إلى: https://api.beitakfemisr.com/api/DIARNANEWDAMIETTA/fieldHandler
[2025-10-06 09:06:32] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:06:33] استجابة API لـ reservationRequest: 401
[2025-10-06 09:06:33] ❌ فشل تحميل خيارات reservationRequest: كود الاستجابة 401
[2025-10-06 09:06:33] تفاصيل الخطأ: {'code': 'AUTHORIZATION_REQUIRED', 'message': 'Authorization Required', 'details': [], 'statusCode': 401, 'supportId': '9bccde90-a27a-11f0-a1ff-77a857010dce'}
[2025-10-06 09:07:39] جاري تسجيل الدخول...
[2025-10-06 09:07:40] ✅ تم تسجيل الدخول بنجاح
[2025-10-06 09:07:40] جاري تحميل الخدمات...
[2025-10-06 09:07:40] استجابة API للخدمات: 200
[2025-10-06 09:07:40] تم استلام بيانات الخدمات: 4 فئة
[2025-10-06 09:07:40] 🔄 تم اختيار الخدمة: بيتك فى مصر (bookunit)
[2025-10-06 09:07:40] جاري تحميل خيارات الاستكمال للخدمة: bookunit
[2025-10-06 09:07:41] استجابة API للاستكمال: 200
[2025-10-06 09:07:41] تم استلام البيانات: 12 عنصر
[2025-10-06 09:08:19] ❌ خطأ في تحميل خيارات الاستكمال: 0
[2025-10-06 09:08:19] تفاصيل الخطأ: Traceback (most recent call last):
  File "E:\Programming\Python\Freelance\Ahmed Abdelnaby\beitakfemisr\beitakfemisr_requests.py", line 636, in load_alternative_completions
    if len(data[0]) == 0:
           ~~~~^^^
KeyError: 0

[2025-10-06 09:08:19] جاري تحميل خيارات model...
[2025-10-06 09:08:19] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-06 09:08:19] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:08:20] استجابة API لـ model: 422
[2025-10-06 09:08:20] ❌ فشل تحميل خيارات model: كود الاستجابة 422
[2025-10-06 09:08:20] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': 'db9fd400-a27a-11f0-8dc6-138fbb2718c1'}
[2025-10-06 09:08:20] جاري تحميل خيارات reservationRequest...
[2025-10-06 09:08:20] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-06 09:08:20] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:08:21] استجابة API لـ reservationRequest: 422
[2025-10-06 09:08:21] ❌ فشل تحميل خيارات reservationRequest: كود الاستجابة 422
[2025-10-06 09:08:21] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': 'dc614f40-a27a-11f0-b3ad-9bc577e65f9b'}
[2025-10-06 09:08:21] ✅ تم تحميل 34 خدمة
[2025-10-06 09:08:34] 🔄 تم اختيار الخدمة: ديارنا - القاهرة الجديدة (DIARNANEWCAIRO)
[2025-10-06 09:08:34] جاري تحميل خيارات الاستكمال للخدمة: DIARNANEWCAIRO
[2025-10-06 09:08:35] استجابة API للاستكمال: 200
[2025-10-06 09:08:35] تم استلام البيانات: 11 عنصر
[2025-10-06 09:11:55] جاري تسجيل الدخول...
[2025-10-06 09:11:56] ✅ تم تسجيل الدخول بنجاح
[2025-10-06 09:11:56] جاري تحميل الخدمات...
[2025-10-06 09:11:57] استجابة API للخدمات: 200
[2025-10-06 09:11:57] تم استلام بيانات الخدمات: 4 فئة
[2025-10-06 09:11:57] 🔄 تم اختيار الخدمة: بيتك فى مصر (bookunit)
[2025-10-06 09:11:57] جاري تحميل خيارات الاستكمال للخدمة: bookunit
[2025-10-06 09:11:58] استجابة API للاستكمال: 200
[2025-10-06 09:11:58] تم استلام البيانات: 12 عنصر
[2025-10-06 09:11:58] ❌ لا يوجد selectUnit في fieldsSchema
[2025-10-06 09:11:58] جاري تحميل خيارات model...
[2025-10-06 09:12:02] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-06 09:12:02] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:12:02] استجابة API لـ model: 422
[2025-10-06 09:12:02] ❌ فشل تحميل خيارات model: كود الاستجابة 422
[2025-10-06 09:12:02] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': '6037eb80-a27b-11f0-b2cf-f371978c6101'}
[2025-10-06 09:12:02] جاري تحميل خيارات reservationRequest...
[2025-10-06 09:12:02] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-06 09:12:02] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:12:03] استجابة API لـ reservationRequest: 422
[2025-10-06 09:12:03] ❌ فشل تحميل خيارات reservationRequest: كود الاستجابة 422
[2025-10-06 09:12:03] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': '607fc9f0-a27b-11f0-b2cf-f371978c6101'}
[2025-10-06 09:12:03] ✅ تم تحميل 34 خدمة
[2025-10-06 09:12:10] 🔄 تم اختيار الخدمة: ديارنا - القاهرة الجديدة (DIARNANEWCAIRO)
[2025-10-06 09:12:10] جاري تحميل خيارات الاستكمال للخدمة: DIARNANEWCAIRO
[2025-10-06 09:12:10] استجابة API للاستكمال: 200
[2025-10-06 09:12:10] تم استلام البيانات: 11 عنصر
[2025-10-06 09:12:10] ✅ تم تحميل 4 خيار للاستكمال
[2025-10-06 09:12:10] جاري تحميل خيارات model...
[2025-10-06 09:12:10] إرسال طلب إلى: https://api.beitakfemisr.com/api/DIARNANEWCAIRO/fieldHandler
[2025-10-06 09:12:10] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:12:11] استجابة API لـ model: 200
[2025-10-06 09:12:11] تم استلام 0 خيار لـ model
[2025-10-06 09:12:11] combo for model: <PyQt5.QtWidgets.QComboBox object at 0x000001A27D524DD0> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-06 09:12:11] ✅ تم تحميل 0 خيار لـ model
[2025-10-06 09:12:11] جاري تحميل خيارات reservationRequest...
[2025-10-06 09:12:11] إرسال طلب إلى: https://api.beitakfemisr.com/api/DIARNANEWCAIRO/fieldHandler
[2025-10-06 09:12:11] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:12:12] استجابة API لـ reservationRequest: 200
[2025-10-06 09:12:12] تم استلام 0 خيار لـ reservationRequest
[2025-10-06 09:12:12] combo for reservationRequest: <PyQt5.QtWidgets.QComboBox object at 0x000001A27D525490> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-06 09:12:12] ✅ تم تحميل 0 خيار لـ reservationRequest
[2025-10-06 09:13:19] 🔄 تم اختيار الخدمة: سكن مصر اكتوبر الجديدة (SAKANMISRNEWOCTOBER)
[2025-10-06 09:13:19] جاري تحميل خيارات الاستكمال للخدمة: SAKANMISRNEWOCTOBER
[2025-10-06 09:13:20] استجابة API للاستكمال: 200
[2025-10-06 09:13:20] تم استلام البيانات: 11 عنصر
[2025-10-06 09:13:20] ✅ تم تحميل 4 خيار للاستكمال
[2025-10-06 09:13:20] جاري تحميل خيارات model...
[2025-10-06 09:13:20] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-06 09:13:20] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:13:21] استجابة API لـ model: 200
[2025-10-06 09:13:21] تم استلام 1 خيار لـ model
[2025-10-06 09:13:21] combo for model: <PyQt5.QtWidgets.QComboBox object at 0x000001A27D524DD0> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-06 09:13:21] جاري تحميل خيارات buildingNumber...
[2025-10-06 09:13:21] إضافة تبعية النموذج: سكن مصر بدون مصعد
[2025-10-06 09:13:21] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-06 09:13:21] بيانات الطلب: {'fieldId': 'buildingNumber', 'data': {'apiDependencies': {}, 'model': 'سكن مصر بدون مصعد'}, 'activityId': 'unitSelect'}
[2025-10-06 09:13:21] استجابة API لـ buildingNumber: 200
[2025-10-06 09:13:21] تم استلام 13 خيار لـ buildingNumber
[2025-10-06 09:13:21] combo for buildingNumber: <PyQt5.QtWidgets.QComboBox object at 0x000001A27D524E60> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-06 09:13:21] جاري تحميل خيارات floor...
[2025-10-06 09:13:21] إضافة تبعية النموذج: سكن مصر بدون مصعد
[2025-10-06 09:13:21] إضافة تبعية المبنى: 126
[2025-10-06 09:13:21] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-06 09:13:21] بيانات الطلب: {'fieldId': 'floor', 'data': {'apiDependencies': {}, 'model': 'سكن مصر بدون مصعد', 'buildingNumber': '126'}, 'activityId': 'unitSelect'}
[2025-10-06 09:13:22] استجابة API لـ floor: 200
[2025-10-06 09:13:22] تم استلام 6 خيار لـ floor
[2025-10-06 09:13:22] combo for floor: <PyQt5.QtWidgets.QComboBox object at 0x000001A27D524EF0> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-06 09:13:22] جاري تحميل خيارات unitNumber...
[2025-10-06 09:13:22] إضافة تبعية النموذج: سكن مصر بدون مصعد
[2025-10-06 09:13:22] إضافة تبعية المبنى: 126
[2025-10-06 09:13:22] إضافة تبعية الدور: 0
[2025-10-06 09:13:22] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-06 09:13:22] بيانات الطلب: {'fieldId': 'unitNumber', 'data': {'apiDependencies': {}, 'model': 'سكن مصر بدون مصعد', 'buildingNumber': '126', 'floor': '0'}, 'activityId': 'unitSelect'}
[2025-10-06 09:13:22] استجابة API لـ unitNumber: 200
[2025-10-06 09:13:22] تم استلام 17 خيار لـ unitNumber
[2025-10-06 09:13:22] combo for unitNumber: <PyQt5.QtWidgets.QComboBox object at 0x000001A27D525370> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-06 09:13:22] ✅ تم تحميل 17 خيار لـ unitNumber
[2025-10-06 09:13:22] ✅ تم تحميل 6 خيار لـ floor
[2025-10-06 09:13:22] ✅ تم تحميل 13 خيار لـ buildingNumber
[2025-10-06 09:13:22] ✅ تم تحميل 1 خيار لـ model
[2025-10-06 09:13:22] جاري تحميل خيارات reservationRequest...
[2025-10-06 09:13:22] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-06 09:13:22] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 09:13:23] استجابة API لـ reservationRequest: 200
[2025-10-06 09:13:23] تم استلام 0 خيار لـ reservationRequest
[2025-10-06 09:13:23] combo for reservationRequest: <PyQt5.QtWidgets.QComboBox object at 0x000001A27D525490> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-06 09:13:23] ✅ تم تحميل 0 خيار لـ reservationRequest
[2025-10-07 10:09:22] جاري تسجيل الدخول...
[2025-10-07 10:09:23] ✅ تم تسجيل الدخول بنجاح
[2025-10-07 10:09:23] جاري تحميل الخدمات...
[2025-10-07 10:09:24] استجابة API للخدمات: 200
[2025-10-07 10:09:24] تم استلام بيانات الخدمات: 4 فئة
[2025-10-07 10:09:24] 🔄 تم اختيار الخدمة: بيتك فى مصر (bookunit)
[2025-10-07 10:09:24] جاري تحميل خيارات الاستكمال للخدمة: bookunit
[2025-10-07 10:09:25] استجابة API للاستكمال: 200
[2025-10-07 10:09:25] تم استلام البيانات: 12 عنصر
[2025-10-07 10:09:25] ❌ لا يوجد selectUnit في fieldsSchema
[2025-10-07 10:09:25] جاري تحميل خيارات model...
[2025-10-07 10:09:25] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-07 10:09:25] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:25] استجابة API لـ model: 422
[2025-10-07 10:09:25] ❌ فشل تحميل خيارات model: كود الاستجابة 422
[2025-10-07 10:09:25] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': '8eb23110-a34c-11f0-ba2f-c9dfed08542e'}
[2025-10-07 10:09:25] جاري تحميل خيارات reservationRequest...
[2025-10-07 10:09:25] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-07 10:09:25] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:25] استجابة API لـ reservationRequest: 422
[2025-10-07 10:09:25] ❌ فشل تحميل خيارات reservationRequest: كود الاستجابة 422
[2025-10-07 10:09:25] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': '8ee35330-a34c-11f0-82b2-afc3c04d7808'}
[2025-10-07 10:09:25] ✅ تم تحميل 34 خدمة
[2025-10-07 10:09:27] 🔄 تم اختيار الخدمة: جاردن سيتى الجديدة (NEWGARDENCITY)
[2025-10-07 10:09:27] جاري تحميل خيارات الاستكمال للخدمة: NEWGARDENCITY
[2025-10-07 10:09:27] استجابة API للاستكمال: 400
[2025-10-07 10:09:27] ❌ فشل تحميل خيارات الاستكمال: كود الاستجابة 400
[2025-10-07 10:09:27] تفاصيل الخطأ: {'code': 'INTEGRATION_ERROR', 'message': 'Integration error', 'details': [{'code': 'ASSERTION_ERROR', 'message': 'لا توجد طلبات جدية حجز تسمح لك التقديم على هذا المشروع', 'details': []}], 'statusCode': 400, 'supportId': '901bfa40-a34c-11f0-acb4-e3bb29027242'}
[2025-10-07 10:09:27] جاري تحميل خيارات model...
[2025-10-07 10:09:27] إرسال طلب إلى: https://api.beitakfemisr.com/api/NEWGARDENCITY/fieldHandler
[2025-10-07 10:09:27] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:28] استجابة API لـ model: 200
[2025-10-07 10:09:28] تم استلام 10 خيار لـ model
[2025-10-07 10:09:28] combo for model: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97770> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-07 10:09:28] جاري تحميل خيارات buildingNumber...
[2025-10-07 10:09:28] إضافة تبعية النموذج: E1
[2025-10-07 10:09:28] إرسال طلب إلى: https://api.beitakfemisr.com/api/NEWGARDENCITY/fieldHandler
[2025-10-07 10:09:28] بيانات الطلب: {'fieldId': 'buildingNumber', 'data': {'apiDependencies': {}, 'model': 'E1'}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:28] استجابة API لـ buildingNumber: 200
[2025-10-07 10:09:28] تم استلام 19 خيار لـ buildingNumber
[2025-10-07 10:09:28] combo for buildingNumber: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97800> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-07 10:09:28] جاري تحميل خيارات floor...
[2025-10-07 10:09:28] إضافة تبعية النموذج: E1
[2025-10-07 10:09:28] إضافة تبعية المبنى: G90-1
[2025-10-07 10:09:28] إرسال طلب إلى: https://api.beitakfemisr.com/api/NEWGARDENCITY/fieldHandler
[2025-10-07 10:09:28] بيانات الطلب: {'fieldId': 'floor', 'data': {'apiDependencies': {}, 'model': 'E1', 'buildingNumber': 'G90-1'}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:29] استجابة API لـ floor: 200
[2025-10-07 10:09:29] تم استلام 7 خيار لـ floor
[2025-10-07 10:09:29] combo for floor: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97890> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-07 10:09:29] جاري تحميل خيارات unitNumber...
[2025-10-07 10:09:29] إضافة تبعية النموذج: E1
[2025-10-07 10:09:29] إضافة تبعية المبنى: G90-1
[2025-10-07 10:09:29] إضافة تبعية الدور: 1
[2025-10-07 10:09:29] إرسال طلب إلى: https://api.beitakfemisr.com/api/NEWGARDENCITY/fieldHandler
[2025-10-07 10:09:29] بيانات الطلب: {'fieldId': 'unitNumber', 'data': {'apiDependencies': {}, 'model': 'E1', 'buildingNumber': 'G90-1', 'floor': '1'}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:30] استجابة API لـ unitNumber: 200
[2025-10-07 10:09:30] تم استلام 12 خيار لـ unitNumber
[2025-10-07 10:09:30] combo for unitNumber: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97D10> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-07 10:09:30] ✅ تم تحميل 12 خيار لـ unitNumber
[2025-10-07 10:09:30] ✅ تم تحميل 7 خيار لـ floor
[2025-10-07 10:09:30] ✅ تم تحميل 19 خيار لـ buildingNumber
[2025-10-07 10:09:30] ✅ تم تحميل 10 خيار لـ model
[2025-10-07 10:09:30] جاري تحميل خيارات reservationRequest...
[2025-10-07 10:09:30] إرسال طلب إلى: https://api.beitakfemisr.com/api/NEWGARDENCITY/fieldHandler
[2025-10-07 10:09:30] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:30] استجابة API لـ reservationRequest: 400
[2025-10-07 10:09:30] ❌ فشل تحميل خيارات reservationRequest: كود الاستجابة 400
[2025-10-07 10:09:30] تفاصيل الخطأ: {'code': 'INTEGRATION_ERROR', 'message': 'Integration error', 'details': [{'code': 'VM_ERROR', 'message': 'response.map is not a function', 'details': []}], 'statusCode': 400, 'supportId': '91c31a90-a34c-11f0-86d6-c3ed25ba60b6'}
[2025-10-07 10:09:44] 🔄 تم اختيار الخدمة: سكن مصر اكتوبر الجديدة (SAKANMISRNEWOCTOBER)
[2025-10-07 10:09:44] جاري تحميل خيارات buildingNumber...
[2025-10-07 10:09:44] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:09:44] بيانات الطلب: {'fieldId': 'buildingNumber', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:44] استجابة API لـ buildingNumber: 200
[2025-10-07 10:09:44] تم استلام 10 خيار لـ buildingNumber
[2025-10-07 10:09:44] combo for buildingNumber: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97800> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: True)
[2025-10-07 10:09:44] جاري تحميل خيارات floor...
[2025-10-07 10:09:44] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:09:44] بيانات الطلب: {'fieldId': 'floor', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:45] استجابة API لـ floor: 200
[2025-10-07 10:09:45] تم استلام 4 خيار لـ floor
[2025-10-07 10:09:45] combo for floor: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97890> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: True)
[2025-10-07 10:09:45] جاري تحميل خيارات unitNumber...
[2025-10-07 10:09:45] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:09:45] بيانات الطلب: {'fieldId': 'unitNumber', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:46] استجابة API لـ unitNumber: 200
[2025-10-07 10:09:46] تم استلام 10 خيار لـ unitNumber
[2025-10-07 10:09:46] combo for unitNumber: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97D10> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: True)
[2025-10-07 10:09:46] ✅ تم تحميل 10 خيار لـ unitNumber
[2025-10-07 10:09:46] جاري تحميل خيارات unitNumber...
[2025-10-07 10:09:46] إضافة تبعية الدور: 0
[2025-10-07 10:09:46] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:09:46] بيانات الطلب: {'fieldId': 'unitNumber', 'data': {'apiDependencies': {}, 'floor': '0'}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:46] استجابة API لـ unitNumber: 200
[2025-10-07 10:09:46] تم استلام 10 خيار لـ unitNumber
[2025-10-07 10:09:46] combo for unitNumber: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97D10> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: True)
[2025-10-07 10:09:46] ✅ تم تحميل 10 خيار لـ unitNumber
[2025-10-07 10:09:46] ✅ تم تحميل 4 خيار لـ floor
[2025-10-07 10:09:46] جاري تحميل خيارات floor...
[2025-10-07 10:09:46] إضافة تبعية المبنى: 126
[2025-10-07 10:09:46] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:09:46] بيانات الطلب: {'fieldId': 'floor', 'data': {'apiDependencies': {}, 'buildingNumber': '126'}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:47] استجابة API لـ floor: 200
[2025-10-07 10:09:47] تم استلام 4 خيار لـ floor
[2025-10-07 10:09:47] combo for floor: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97890> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: True)
[2025-10-07 10:09:47] جاري تحميل خيارات unitNumber...
[2025-10-07 10:09:47] إضافة تبعية المبنى: 126
[2025-10-07 10:09:47] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:09:47] بيانات الطلب: {'fieldId': 'unitNumber', 'data': {'apiDependencies': {}, 'buildingNumber': '126'}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:47] استجابة API لـ unitNumber: 200
[2025-10-07 10:09:47] تم استلام 10 خيار لـ unitNumber
[2025-10-07 10:09:47] combo for unitNumber: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97D10> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: True)
[2025-10-07 10:09:47] ✅ تم تحميل 10 خيار لـ unitNumber
[2025-10-07 10:09:47] جاري تحميل خيارات unitNumber...
[2025-10-07 10:09:47] إضافة تبعية المبنى: 126
[2025-10-07 10:09:47] إضافة تبعية الدور: 0
[2025-10-07 10:09:47] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:09:47] بيانات الطلب: {'fieldId': 'unitNumber', 'data': {'apiDependencies': {}, 'buildingNumber': '126', 'floor': '0'}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:48] استجابة API لـ unitNumber: 200
[2025-10-07 10:09:48] تم استلام 10 خيار لـ unitNumber
[2025-10-07 10:09:48] combo for unitNumber: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97D10> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: True)
[2025-10-07 10:09:48] ✅ تم تحميل 10 خيار لـ unitNumber
[2025-10-07 10:09:48] ✅ تم تحميل 4 خيار لـ floor
[2025-10-07 10:09:48] ✅ تم تحميل 10 خيار لـ buildingNumber
[2025-10-07 10:09:48] جاري تحميل خيارات floor...
[2025-10-07 10:09:48] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:09:48] بيانات الطلب: {'fieldId': 'floor', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:49] استجابة API لـ floor: 200
[2025-10-07 10:09:49] تم استلام 4 خيار لـ floor
[2025-10-07 10:09:49] combo for floor: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97890> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: True)
[2025-10-07 10:09:49] جاري تحميل خيارات unitNumber...
[2025-10-07 10:09:49] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:09:49] بيانات الطلب: {'fieldId': 'unitNumber', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:50] استجابة API لـ unitNumber: 200
[2025-10-07 10:09:50] تم استلام 10 خيار لـ unitNumber
[2025-10-07 10:09:50] combo for unitNumber: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97D10> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: True)
[2025-10-07 10:09:50] ✅ تم تحميل 10 خيار لـ unitNumber
[2025-10-07 10:09:50] جاري تحميل خيارات unitNumber...
[2025-10-07 10:09:50] إضافة تبعية الدور: 0
[2025-10-07 10:09:50] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:09:50] بيانات الطلب: {'fieldId': 'unitNumber', 'data': {'apiDependencies': {}, 'floor': '0'}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:53] استجابة API لـ unitNumber: 200
[2025-10-07 10:09:53] تم استلام 10 خيار لـ unitNumber
[2025-10-07 10:09:53] combo for unitNumber: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97D10> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: True)
[2025-10-07 10:09:53] ✅ تم تحميل 10 خيار لـ unitNumber
[2025-10-07 10:09:53] ✅ تم تحميل 4 خيار لـ floor
[2025-10-07 10:09:53] جاري تحميل خيارات unitNumber...
[2025-10-07 10:09:53] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:09:53] بيانات الطلب: {'fieldId': 'unitNumber', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:53] استجابة API لـ unitNumber: 200
[2025-10-07 10:09:53] تم استلام 10 خيار لـ unitNumber
[2025-10-07 10:09:53] combo for unitNumber: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97D10> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: True)
[2025-10-07 10:09:53] ✅ تم تحميل 10 خيار لـ unitNumber
[2025-10-07 10:09:53] جاري تحميل خيارات الاستكمال للخدمة: SAKANMISRNEWOCTOBER
[2025-10-07 10:09:54] استجابة API للاستكمال: 200
[2025-10-07 10:09:54] تم استلام البيانات: 11 عنصر
[2025-10-07 10:09:54] ✅ تم تحميل 4 خيار للاستكمال
[2025-10-07 10:09:54] جاري تحميل خيارات model...
[2025-10-07 10:09:54] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:09:54] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:54] استجابة API لـ model: 200
[2025-10-07 10:09:54] تم استلام 1 خيار لـ model
[2025-10-07 10:09:54] combo for model: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97770> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-07 10:09:54] جاري تحميل خيارات buildingNumber...
[2025-10-07 10:09:54] إضافة تبعية النموذج: سكن مصر بدون مصعد
[2025-10-07 10:09:54] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:09:54] بيانات الطلب: {'fieldId': 'buildingNumber', 'data': {'apiDependencies': {}, 'model': 'سكن مصر بدون مصعد'}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:55] استجابة API لـ buildingNumber: 200
[2025-10-07 10:09:55] تم استلام 10 خيار لـ buildingNumber
[2025-10-07 10:09:55] combo for buildingNumber: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97800> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-07 10:09:55] جاري تحميل خيارات floor...
[2025-10-07 10:09:55] إضافة تبعية النموذج: سكن مصر بدون مصعد
[2025-10-07 10:09:55] إضافة تبعية المبنى: 126
[2025-10-07 10:09:55] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:09:55] بيانات الطلب: {'fieldId': 'floor', 'data': {'apiDependencies': {}, 'model': 'سكن مصر بدون مصعد', 'buildingNumber': '126'}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:55] استجابة API لـ floor: 200
[2025-10-07 10:09:55] تم استلام 4 خيار لـ floor
[2025-10-07 10:09:55] combo for floor: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97890> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-07 10:09:55] جاري تحميل خيارات unitNumber...
[2025-10-07 10:09:55] إضافة تبعية النموذج: سكن مصر بدون مصعد
[2025-10-07 10:09:55] إضافة تبعية المبنى: 126
[2025-10-07 10:09:55] إضافة تبعية الدور: 0
[2025-10-07 10:09:55] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:09:55] بيانات الطلب: {'fieldId': 'unitNumber', 'data': {'apiDependencies': {}, 'model': 'سكن مصر بدون مصعد', 'buildingNumber': '126', 'floor': '0'}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:56] استجابة API لـ unitNumber: 200
[2025-10-07 10:09:56] تم استلام 10 خيار لـ unitNumber
[2025-10-07 10:09:56] combo for unitNumber: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97D10> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-07 10:09:56] ✅ تم تحميل 10 خيار لـ unitNumber
[2025-10-07 10:09:56] ✅ تم تحميل 4 خيار لـ floor
[2025-10-07 10:09:56] ✅ تم تحميل 10 خيار لـ buildingNumber
[2025-10-07 10:09:56] ✅ تم تحميل 1 خيار لـ model
[2025-10-07 10:09:56] جاري تحميل خيارات reservationRequest...
[2025-10-07 10:09:56] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:09:56] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-07 10:09:56] استجابة API لـ reservationRequest: 200
[2025-10-07 10:09:56] تم استلام 1 خيار لـ reservationRequest
[2025-10-07 10:09:56] combo for reservationRequest: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97E30> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-07 10:09:56] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 10:10:02] جاري تحميل خيارات floor...
[2025-10-07 10:10:02] إضافة تبعية النموذج: سكن مصر بدون مصعد
[2025-10-07 10:10:02] إضافة تبعية المبنى: 36
[2025-10-07 10:10:02] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:10:02] بيانات الطلب: {'fieldId': 'floor', 'data': {'apiDependencies': {}, 'model': 'سكن مصر بدون مصعد', 'buildingNumber': '36'}, 'activityId': 'unitSelect'}
[2025-10-07 10:10:03] استجابة API لـ floor: 200
[2025-10-07 10:10:03] تم استلام 4 خيار لـ floor
[2025-10-07 10:10:03] combo for floor: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97890> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: True)
[2025-10-07 10:10:03] جاري تحميل خيارات unitNumber...
[2025-10-07 10:10:03] إضافة تبعية النموذج: سكن مصر بدون مصعد
[2025-10-07 10:10:03] إضافة تبعية المبنى: 36
[2025-10-07 10:10:03] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:10:03] بيانات الطلب: {'fieldId': 'unitNumber', 'data': {'apiDependencies': {}, 'model': 'سكن مصر بدون مصعد', 'buildingNumber': '36'}, 'activityId': 'unitSelect'}
[2025-10-07 10:10:03] استجابة API لـ unitNumber: 200
[2025-10-07 10:10:03] تم استلام 10 خيار لـ unitNumber
[2025-10-07 10:10:03] combo for unitNumber: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97D10> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: True)
[2025-10-07 10:10:03] ✅ تم تحميل 10 خيار لـ unitNumber
[2025-10-07 10:10:03] جاري تحميل خيارات unitNumber...
[2025-10-07 10:10:03] إضافة تبعية النموذج: سكن مصر بدون مصعد
[2025-10-07 10:10:03] إضافة تبعية المبنى: 36
[2025-10-07 10:10:03] إضافة تبعية الدور: 0
[2025-10-07 10:10:03] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:10:03] بيانات الطلب: {'fieldId': 'unitNumber', 'data': {'apiDependencies': {}, 'model': 'سكن مصر بدون مصعد', 'buildingNumber': '36', 'floor': '0'}, 'activityId': 'unitSelect'}
[2025-10-07 10:10:04] استجابة API لـ unitNumber: 200
[2025-10-07 10:10:04] تم استلام 10 خيار لـ unitNumber
[2025-10-07 10:10:04] combo for unitNumber: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97D10> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: True)
[2025-10-07 10:10:04] ✅ تم تحميل 10 خيار لـ unitNumber
[2025-10-07 10:10:04] ✅ تم تحميل 4 خيار لـ floor
[2025-10-07 10:10:05] جاري تحميل خيارات unitNumber...
[2025-10-07 10:10:05] إضافة تبعية النموذج: سكن مصر بدون مصعد
[2025-10-07 10:10:05] إضافة تبعية المبنى: 36
[2025-10-07 10:10:05] إضافة تبعية الدور: 4
[2025-10-07 10:10:05] إرسال طلب إلى: https://api.beitakfemisr.com/api/SAKANMISRNEWOCTOBER/fieldHandler
[2025-10-07 10:10:05] بيانات الطلب: {'fieldId': 'unitNumber', 'data': {'apiDependencies': {}, 'model': 'سكن مصر بدون مصعد', 'buildingNumber': '36', 'floor': '4'}, 'activityId': 'unitSelect'}
[2025-10-07 10:10:05] استجابة API لـ unitNumber: 200
[2025-10-07 10:10:05] تم استلام 10 خيار لـ unitNumber
[2025-10-07 10:10:05] combo for unitNumber: <PyQt5.QtWidgets.QComboBox object at 0x0000029FD5A97D10> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: True)
[2025-10-07 10:10:05] ✅ تم تحميل 10 خيار لـ unitNumber
[2025-10-07 10:10:07] ✅ تمت إضافة محاولة: سكن مصر بدون مصعد - 36 - الرابع - 1 - قيمة 50% استكمال البديل الأول
[2025-10-07 10:10:10] 🔍 جاري اختبار الحصول على تفاصيل الوحدة...
[2025-10-07 10:10:10] ❌ فشل الحصول على تفاصيل الوحدة: كود الاستجابة 400
[2025-10-07 10:10:10] تفاصيل الخطأ: {'code': 'INTEGRATION_ERROR', 'message': 'Integration error', 'details': [{'code': 'ASSERTION_ERROR', 'message': 'برجاء احتيار وحدة اخرى حيث انه تم حجز الوحدة من قبل مواطن اخر', 'details': []}], 'statusCode': 400, 'supportId': 'a98d0a00-a34c-11f0-a8a9-51e074d4aeef'}
[2025-10-07 10:14:51] جاري تسجيل الدخول...
[2025-10-07 10:14:51] ✅ تم تسجيل الدخول بنجاح
[2025-10-07 10:14:51] جاري تحميل الخدمات...
[2025-10-07 10:14:52] استجابة API للخدمات: 200
[2025-10-07 10:14:52] تم استلام بيانات الخدمات: 4 فئة
[2025-10-07 10:14:52] 🔄 تم اختيار الخدمة: بيتك فى مصر (bookunit)
[2025-10-07 10:14:52] جاري تحميل خيارات الاستكمال للخدمة: bookunit
[2025-10-07 10:14:53] استجابة API للاستكمال: 200
[2025-10-07 10:14:53] تم استلام البيانات: 12 عنصر
[2025-10-07 10:14:53] ❌ لا يوجد selectUnit في fieldsSchema
[2025-10-07 10:14:53] جاري تحميل خيارات model...
[2025-10-07 10:14:53] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-07 10:14:53] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-07 10:14:53] استجابة API لـ model: 422
[2025-10-07 10:14:53] ❌ فشل تحميل خيارات model: كود الاستجابة 422
[2025-10-07 10:14:53] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': '525978d0-a34d-11f0-874a-21549da114a0'}
[2025-10-07 10:14:53] جاري تحميل خيارات reservationRequest...
[2025-10-07 10:14:53] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-07 10:14:53] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-07 10:14:54] استجابة API لـ reservationRequest: 422
[2025-10-07 10:14:54] ❌ فشل تحميل خيارات reservationRequest: كود الاستجابة 422
[2025-10-07 10:14:54] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': '529e22f0-a34d-11f0-aa82-e9dfafee14f0'}
[2025-10-07 10:14:54] ✅ تم تحميل 34 خدمة
[2025-10-07 10:15:01] 🔄 تم اختيار الخدمة: فالى تاورز - حدائق اكتوبر (VALLEYTOWERSOCTOBER)
[2025-10-07 10:15:01] جاري تحميل خيارات الاستكمال للخدمة: VALLEYTOWERSOCTOBER
[2025-10-07 10:15:02] استجابة API للاستكمال: 200
[2025-10-07 10:15:02] تم استلام البيانات: 11 عنصر
[2025-10-07 10:15:02] ✅ تم تحميل 4 خيار للاستكمال
[2025-10-07 10:15:02] جاري تحميل خيارات model...
[2025-10-07 10:15:02] إرسال طلب إلى: https://api.beitakfemisr.com/api/VALLEYTOWERSOCTOBER/fieldHandler
[2025-10-07 10:15:02] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-07 10:15:02] استجابة API لـ model: 200
[2025-10-07 10:15:02] تم استلام 1 خيار لـ model
[2025-10-07 10:15:02] combo for model: <PyQt5.QtWidgets.QComboBox object at 0x000002A11FAFC050> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-07 10:15:02] جاري تحميل خيارات buildingNumber...
[2025-10-07 10:15:02] إضافة تبعية النموذج: الأبراج
[2025-10-07 10:15:02] إرسال طلب إلى: https://api.beitakfemisr.com/api/VALLEYTOWERSOCTOBER/fieldHandler
[2025-10-07 10:15:02] بيانات الطلب: {'fieldId': 'buildingNumber', 'data': {'apiDependencies': {}, 'model': 'الأبراج'}, 'activityId': 'unitSelect'}
[2025-10-07 10:15:03] استجابة API لـ buildingNumber: 200
[2025-10-07 10:15:03] تم استلام 2 خيار لـ buildingNumber
[2025-10-07 10:15:03] combo for buildingNumber: <PyQt5.QtWidgets.QComboBox object at 0x000002A11FAFC0E0> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-07 10:15:03] جاري تحميل خيارات floor...
[2025-10-07 10:15:03] إضافة تبعية النموذج: الأبراج
[2025-10-07 10:15:03] إضافة تبعية المبنى: 71
[2025-10-07 10:15:03] إرسال طلب إلى: https://api.beitakfemisr.com/api/VALLEYTOWERSOCTOBER/fieldHandler
[2025-10-07 10:15:03] بيانات الطلب: {'fieldId': 'floor', 'data': {'apiDependencies': {}, 'model': 'الأبراج', 'buildingNumber': '71'}, 'activityId': 'unitSelect'}
[2025-10-07 10:15:03] استجابة API لـ floor: 200
[2025-10-07 10:15:03] تم استلام 1 خيار لـ floor
[2025-10-07 10:15:03] combo for floor: <PyQt5.QtWidgets.QComboBox object at 0x000002A11FAFC170> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-07 10:15:03] جاري تحميل خيارات unitNumber...
[2025-10-07 10:15:03] إضافة تبعية النموذج: الأبراج
[2025-10-07 10:15:03] إضافة تبعية المبنى: 71
[2025-10-07 10:15:03] إضافة تبعية الدور: last
[2025-10-07 10:15:03] إرسال طلب إلى: https://api.beitakfemisr.com/api/VALLEYTOWERSOCTOBER/fieldHandler
[2025-10-07 10:15:03] بيانات الطلب: {'fieldId': 'unitNumber', 'data': {'apiDependencies': {}, 'model': 'الأبراج', 'buildingNumber': '71', 'floor': 'last'}, 'activityId': 'unitSelect'}
[2025-10-07 10:15:03] استجابة API لـ unitNumber: 200
[2025-10-07 10:15:04] تم استلام 2 خيار لـ unitNumber
[2025-10-07 10:15:04] combo for unitNumber: <PyQt5.QtWidgets.QComboBox object at 0x000002A11FAFC5F0> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-07 10:15:04] ✅ تم تحميل 2 خيار لـ unitNumber
[2025-10-07 10:15:04] ✅ تم تحميل 1 خيار لـ floor
[2025-10-07 10:15:04] ✅ تم تحميل 2 خيار لـ buildingNumber
[2025-10-07 10:15:04] ✅ تم تحميل 1 خيار لـ model
[2025-10-07 10:15:04] جاري تحميل خيارات reservationRequest...
[2025-10-07 10:15:04] إرسال طلب إلى: https://api.beitakfemisr.com/api/VALLEYTOWERSOCTOBER/fieldHandler
[2025-10-07 10:15:04] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-07 10:15:04] استجابة API لـ reservationRequest: 200
[2025-10-07 10:15:04] تم استلام 1 خيار لـ reservationRequest
[2025-10-07 10:15:04] combo for reservationRequest: <PyQt5.QtWidgets.QComboBox object at 0x000002A11FAFC710> (type: <class 'PyQt5.QtWidgets.QComboBox'>, bool: False)
[2025-10-07 10:15:04] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 10:15:11] ✅ تمت إضافة محاولة: الأبراج - 71 - الأخير - 56 - قيمة 50% استكمال البديل الأول
[2025-10-07 10:15:20] 🔍 جاري اختبار الحصول على تفاصيل الوحدة...
[2025-10-07 10:15:20] ❌ فشل الحصول على تفاصيل الوحدة: كود الاستجابة 400
[2025-10-07 10:15:20] تفاصيل الخطأ: {'code': 'INTEGRATION_ERROR', 'message': 'Integration error', 'details': [{'code': 'ASSERTION_ERROR', 'message': 'برجاء احتيار وحدة اخرى حيث انه تم حجز الوحدة من قبل مواطن اخر', 'details': []}], 'statusCode': 400, 'supportId': '6238e330-a34d-11f0-9e82-fd76000db19f'}
[2025-10-07 10:16:04] 🔍 جاري اختبار الحصول على تفاصيل الوحدة...
[2025-10-07 10:19:34] ❌ فشل الحصول على تفاصيل الوحدة: كود الاستجابة 400
[2025-10-07 10:19:34] تفاصيل الخطأ: {'code': 'INTEGRATION_ERROR', 'message': 'Integration error', 'details': [{'code': 'ASSERTION_ERROR', 'message': 'برجاء احتيار وحدة اخرى حيث انه تم حجز الوحدة من قبل مواطن اخر', 'details': []}], 'statusCode': 400, 'supportId': 'a446fb90-a34d-11f0-b2cf-f371978c6101'}
[2025-10-07 10:19:41] 🔍 جاري اختبار الحصول على تفاصيل الوحدة...
[2025-10-07 10:19:47] ✅ تم الحصول على تفاصيل الوحدة بنجاح
[2025-10-07 10:19:47] البيانات الخام: {
  "id": "68cbe5695b697f00079ac073",
  "city": "city",
  "region": "region",
  "model": "الأبراج",
  "buildingNumber": "71",
  "floor": "الأخير",
  "unitNumber": "57",
  "unitType": "unit",
  "unitCode": "08-71-57",
  "area": "97",
  "roomCount": "3",
  "finishing": "full",
  "pricePerMeter": "18,000",
  "unitRemainingReservationFeesUSD": 11760.73,
  "adminFeesUSD": "541",
  "excellencePercentages": "0%",
  "alternativeCompletion": "firstAlternativeCompletion50"
}
[2025-10-07 10:20:52] 🔍 جاري اختبار الحصول على تفاصيل الوحدة...
[2025-10-07 10:20:52] ❌ فشل الحصول على تفاصيل الوحدة: كود الاستجابة 401
[2025-10-07 10:20:52] تفاصيل الخطأ: {'code': 'AUTHORIZATION_REQUIRED', 'message': 'Authorization Required', 'details': [], 'statusCode': 401, 'supportId': '2859dd80-a34e-11f0-874a-21549da114a0'}
[2025-10-07 11:53:52] جاري تسجيل الدخول...
[2025-10-07 11:53:52] ✅ تم تسجيل الدخول بنجاح
[2025-10-07 11:53:53] جاري تحميل الخدمات...
[2025-10-07 11:53:53] 🔄 تم اختيار الخدمة: بيتك فى مصر
[2025-10-07 11:53:53] جاري تحميل خيارات الاستكمال للخدمة: bookunit
[2025-10-07 11:54:07] ❌ خطأ في تحميل خيارات الاستكمال: 'selectUnit'
[2025-10-07 11:54:07] جاري تحميل خيارات reservationRequest...
[2025-10-07 11:54:07] ❌ فشل تحميل reservationRequest: 422 - {"code":"VALIDATION_ERROR","message":"Missing or invalid hookId","details":[],"statusCode":422,"supportId":"2f4a3830-a35b-11f0-8dc6-138fbb2718c1"}
[2025-10-07 11:54:07] ✅ تم تحميل 34 خدمة
[2025-10-07 11:54:20] 🔄 تم اختيار الخدمة: فالى تاورز - حدائق اكتوبر
[2025-10-07 11:54:20] جاري تحميل خيارات الاستكمال للخدمة: VALLEYTOWERSOCTOBER
[2025-10-07 11:54:20] ✅ تم تحميل 4 خيار للاستكمال
[2025-10-07 11:54:20] جاري تحميل خيارات reservationRequest...
[2025-10-07 11:54:20] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 11:54:20] جاري تحميل خيارات model...
[2025-10-07 11:54:21] ✅ تم تحميل 1 خيار لـ model
[2025-10-07 11:54:21] جاري تحميل خيارات buildingNumber...
[2025-10-07 11:54:21] ✅ تم تحميل 2 خيار لـ buildingNumber
[2025-10-07 11:54:21] جاري تحميل خيارات floor...
[2025-10-07 11:54:22] ✅ تم تحميل 1 خيار لـ floor
[2025-10-07 11:54:22] جاري تحميل خيارات unitNumber...
[2025-10-07 11:54:22] ✅ تم تحميل 2 خيار لـ unitNumber
[2025-10-07 11:54:27] جاري تحميل خيارات floor...
[2025-10-07 11:54:27] ✅ تم تحميل 1 خيار لـ floor
[2025-10-07 11:54:27] جاري تحميل خيارات unitNumber...
[2025-10-07 11:54:27] ✅ تم تحميل 2 خيار لـ unitNumber
[2025-10-07 11:54:29] جاري تحميل خيارات floor...
[2025-10-07 11:54:29] ✅ تم تحميل 1 خيار لـ floor
[2025-10-07 11:54:29] جاري تحميل خيارات unitNumber...
[2025-10-07 11:54:30] ✅ تم تحميل 2 خيار لـ unitNumber
[2025-10-07 11:54:34] جاري تحميل خيارات floor...
[2025-10-07 11:54:35] ✅ تم تحميل 1 خيار لـ floor
[2025-10-07 11:54:35] جاري تحميل خيارات unitNumber...
[2025-10-07 11:54:35] ✅ تم تحميل 2 خيار لـ unitNumber
[2025-10-07 11:54:40] جاري تحميل خيارات model...
[2025-10-07 11:54:42] ✅ تم تحميل 1 خيار لـ model
[2025-10-07 11:54:42] جاري تحميل خيارات buildingNumber...
[2025-10-07 11:54:43] ✅ تم تحميل 2 خيار لـ buildingNumber
[2025-10-07 11:54:43] جاري تحميل خيارات floor...
[2025-10-07 11:54:44] ✅ تم تحميل 1 خيار لـ floor
[2025-10-07 11:54:44] جاري تحميل خيارات unitNumber...
[2025-10-07 11:54:44] ✅ تم تحميل 2 خيار لـ unitNumber
[2025-10-07 11:54:44] جاري تحميل خيارات reservationRequest...
[2025-10-07 11:54:45] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 11:54:45] جاري تحميل خيارات model...
[2025-10-07 11:54:45] ✅ تم تحميل 1 خيار لـ model
[2025-10-07 11:54:45] جاري تحميل خيارات buildingNumber...
[2025-10-07 11:54:46] ✅ تم تحميل 2 خيار لـ buildingNumber
[2025-10-07 11:54:46] جاري تحميل خيارات floor...
[2025-10-07 11:54:46] ✅ تم تحميل 1 خيار لـ floor
[2025-10-07 11:54:46] جاري تحميل خيارات unitNumber...
[2025-10-07 11:54:47] ✅ تم تحميل 2 خيار لـ unitNumber
[2025-10-07 11:54:47] جاري تحميل خيارات reservationRequest...
[2025-10-07 11:54:47] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 11:54:47] جاري تحميل خيارات model...
[2025-10-07 11:54:48] ✅ تم تحميل 1 خيار لـ model
[2025-10-07 11:54:48] جاري تحميل خيارات buildingNumber...
[2025-10-07 11:54:48] ✅ تم تحميل 2 خيار لـ buildingNumber
[2025-10-07 11:54:48] جاري تحميل خيارات floor...
[2025-10-07 11:54:50] ✅ تم تحميل 1 خيار لـ floor
[2025-10-07 11:54:50] جاري تحميل خيارات unitNumber...
[2025-10-07 11:54:50] ✅ تم تحميل 2 خيار لـ unitNumber
[2025-10-07 11:54:50] جاري تحميل خيارات model...
[2025-10-07 11:54:51] ✅ تم تحميل 1 خيار لـ model
[2025-10-07 11:54:51] جاري تحميل خيارات buildingNumber...
[2025-10-07 11:54:51] ✅ تم تحميل 2 خيار لـ buildingNumber
[2025-10-07 11:54:51] جاري تحميل خيارات floor...
[2025-10-07 11:54:52] ✅ تم تحميل 1 خيار لـ floor
[2025-10-07 11:54:52] جاري تحميل خيارات unitNumber...
[2025-10-07 11:54:52] ✅ تم تحميل 2 خيار لـ unitNumber
[2025-10-07 11:54:52] جاري تحميل خيارات reservationRequest...
[2025-10-07 11:54:53] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 11:54:53] جاري تحميل خيارات model...
[2025-10-07 11:54:53] ✅ تم تحميل 1 خيار لـ model
[2025-10-07 11:54:53] جاري تحميل خيارات buildingNumber...
[2025-10-07 11:54:54] ✅ تم تحميل 2 خيار لـ buildingNumber
[2025-10-07 11:54:54] جاري تحميل خيارات floor...
[2025-10-07 11:54:54] ✅ تم تحميل 1 خيار لـ floor
[2025-10-07 11:54:54] جاري تحميل خيارات unitNumber...
[2025-10-07 11:54:55] ✅ تم تحميل 2 خيار لـ unitNumber
[2025-10-07 11:54:55] جاري تحميل خيارات reservationRequest...
[2025-10-07 11:54:55] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 11:54:55] جاري تحميل خيارات model...
[2025-10-07 11:54:56] ✅ تم تحميل 1 خيار لـ model
[2025-10-07 11:54:56] جاري تحميل خيارات buildingNumber...
[2025-10-07 11:54:57] ✅ تم تحميل 2 خيار لـ buildingNumber
[2025-10-07 11:54:57] جاري تحميل خيارات floor...
[2025-10-07 11:54:58] ✅ تم تحميل 1 خيار لـ floor
[2025-10-07 11:54:58] جاري تحميل خيارات unitNumber...
[2025-10-07 11:54:59] ✅ تم تحميل 2 خيار لـ unitNumber
[2025-10-07 11:55:01] جاري تحميل خيارات model...
[2025-10-07 11:55:01] ✅ تم تحميل 1 خيار لـ model
[2025-10-07 11:55:01] جاري تحميل خيارات buildingNumber...
[2025-10-07 11:55:02] ✅ تم تحميل 2 خيار لـ buildingNumber
[2025-10-07 11:55:02] جاري تحميل خيارات floor...
[2025-10-07 11:55:02] ✅ تم تحميل 1 خيار لـ floor
[2025-10-07 11:55:02] جاري تحميل خيارات unitNumber...
[2025-10-07 11:55:03] ✅ تم تحميل 2 خيار لـ unitNumber
[2025-10-07 11:55:03] جاري تحميل خيارات reservationRequest...
[2025-10-07 11:55:04] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 11:55:04] جاري تحميل خيارات model...
[2025-10-07 11:55:04] ✅ تم تحميل 1 خيار لـ model
[2025-10-07 11:55:04] جاري تحميل خيارات buildingNumber...
[2025-10-07 11:55:04] ✅ تم تحميل 2 خيار لـ buildingNumber
[2025-10-07 11:55:04] جاري تحميل خيارات floor...
[2025-10-07 11:55:05] ✅ تم تحميل 1 خيار لـ floor
[2025-10-07 11:55:05] جاري تحميل خيارات unitNumber...
[2025-10-07 11:55:05] ✅ تم تحميل 2 خيار لـ unitNumber
[2025-10-07 11:55:08] جاري تحميل خيارات floor...
[2025-10-07 11:55:09] ✅ تم تحميل 1 خيار لـ floor
[2025-10-07 11:55:09] جاري تحميل خيارات unitNumber...
[2025-10-07 11:55:09] ✅ تم تحميل 2 خيار لـ unitNumber
[2025-10-07 11:55:10] جاري تحميل خيارات floor...
[2025-10-07 11:55:11] ✅ تم تحميل 1 خيار لـ floor
[2025-10-07 11:55:11] جاري تحميل خيارات unitNumber...
[2025-10-07 11:55:14] ✅ تم تحميل 2 خيار لـ unitNumber
[2025-10-07 11:55:21] جاري تحميل خيارات floor...
[2025-10-07 11:55:22] ✅ تم تحميل 1 خيار لـ floor
[2025-10-07 11:55:22] جاري تحميل خيارات unitNumber...
[2025-10-07 11:55:23] ✅ تم تحميل 2 خيار لـ unitNumber
[2025-10-07 11:55:40] 🔄 تم اختيار الخدمة: ديارنا - 15 مايو
[2025-10-07 11:55:40] جاري تحميل خيارات الاستكمال للخدمة: DIARNAFIFTEENMAY
[2025-10-07 11:55:41] ✅ تم تحميل 4 خيار للاستكمال
[2025-10-07 11:55:41] جاري تحميل خيارات model...
[2025-10-07 11:55:41] ✅ تم تحميل 2 خيار لـ model
[2025-10-07 11:55:41] جاري تحميل خيارات buildingNumber...
[2025-10-07 11:55:42] ✅ تم تحميل 13 خيار لـ buildingNumber
[2025-10-07 11:55:42] جاري تحميل خيارات floor...
[2025-10-07 11:55:42] ✅ تم تحميل 7 خيار لـ floor
[2025-10-07 11:55:42] جاري تحميل خيارات unitNumber...
[2025-10-07 11:55:44] ✅ تم تحميل 26 خيار لـ unitNumber
[2025-10-07 11:55:44] جاري تحميل خيارات reservationRequest...
[2025-10-07 11:55:45] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 11:55:45] جاري تحميل خيارات model...
[2025-10-07 11:55:45] ✅ تم تحميل 2 خيار لـ model
[2025-10-07 11:55:45] جاري تحميل خيارات buildingNumber...
[2025-10-07 11:55:46] ✅ تم تحميل 13 خيار لـ buildingNumber
[2025-10-07 11:55:46] جاري تحميل خيارات floor...
[2025-10-07 11:55:47] ✅ تم تحميل 7 خيار لـ floor
[2025-10-07 11:55:47] جاري تحميل خيارات unitNumber...
[2025-10-07 11:55:47] ✅ تم تحميل 26 خيار لـ unitNumber
[2025-10-07 11:55:49] جاري تحميل خيارات buildingNumber...
[2025-10-07 11:55:50] ✅ تم تحميل 13 خيار لـ buildingNumber
[2025-10-07 11:55:50] جاري تحميل خيارات floor...
[2025-10-07 11:55:50] ✅ تم تحميل 7 خيار لـ floor
[2025-10-07 11:55:50] جاري تحميل خيارات unitNumber...
[2025-10-07 11:55:51] ✅ تم تحميل 26 خيار لـ unitNumber
[2025-10-07 11:55:54] جاري تحميل خيارات reservationRequest...
[2025-10-07 11:55:54] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 11:55:54] جاري تحميل خيارات model...
[2025-10-07 11:55:55] ✅ تم تحميل 2 خيار لـ model
[2025-10-07 11:55:55] جاري تحميل خيارات buildingNumber...
[2025-10-07 11:55:55] ✅ تم تحميل 13 خيار لـ buildingNumber
[2025-10-07 11:55:55] جاري تحميل خيارات floor...
[2025-10-07 11:55:56] ✅ تم تحميل 7 خيار لـ floor
[2025-10-07 11:55:56] جاري تحميل خيارات unitNumber...
[2025-10-07 11:55:59] ✅ تم تحميل 26 خيار لـ unitNumber
[2025-10-07 11:56:04] جاري تحميل خيارات floor...
[2025-10-07 11:56:04] ✅ تم تحميل 7 خيار لـ floor
[2025-10-07 11:56:04] جاري تحميل خيارات unitNumber...
[2025-10-07 11:56:06] ✅ تم تحميل 26 خيار لـ unitNumber
[2025-10-07 11:56:13] جاري تحميل خيارات buildingNumber...
[2025-10-07 11:56:14] ✅ تم تحميل 13 خيار لـ buildingNumber
[2025-10-07 11:56:14] جاري تحميل خيارات floor...
[2025-10-07 11:56:15] ✅ تم تحميل 7 خيار لـ floor
[2025-10-07 11:56:15] جاري تحميل خيارات unitNumber...
[2025-10-07 11:56:15] ✅ تم تحميل 26 خيار لـ unitNumber
[2025-10-07 12:05:41] جاري تسجيل الدخول...
[2025-10-07 12:05:42] ✅ تم تسجيل الدخول بنجاح
[2025-10-07 12:05:42] جاري تحميل الخدمات...
[2025-10-07 12:05:43] 🔄 تم اختيار الخدمة: بيتك فى مصر
[2025-10-07 12:05:43] جاري تحميل خيارات الاستكمال للخدمة: bookunit
[2025-10-07 12:05:48] ❌ خطأ في تحميل خيارات الاستكمال: 'selectUnit'
[2025-10-07 12:05:48] جاري تحميل خيارات reservationRequest...
[2025-10-07 12:05:48] ❌ فشل تحميل reservationRequest: 422 - {"code":"VALIDATION_ERROR","message":"Missing or invalid hookId","details":[],"statusCode":422,"supportId":"d1059c90-a35c-11f0-9ff7-535ab0cfb804"}
[2025-10-07 12:05:48] ✅ تم تحميل 34 خدمة
[2025-10-07 12:05:55] 🔄 تم اختيار الخدمة: ديارنا - 15 مايو
[2025-10-07 12:05:55] جاري تحميل خيارات الاستكمال للخدمة: DIARNAFIFTEENMAY
[2025-10-07 12:05:55] ✅ تم تحميل 4 خيار للاستكمال
[2025-10-07 12:05:55] جاري تحميل خيارات reservationRequest...
[2025-10-07 12:05:55] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 12:15:47] جاري تسجيل الدخول...
[2025-10-07 12:15:47] ✅ تم تسجيل الدخول بنجاح
[2025-10-07 12:15:47] جاري تحميل الخدمات...
[2025-10-07 12:15:48] 🔄 تم اختيار الخدمة: بيتك فى مصر
[2025-10-07 12:15:48] جاري تحميل خيارات الاستكمال للخدمة: bookunit
[2025-10-07 12:15:50] ❌ خطأ في تحميل خيارات الاستكمال: 'selectUnit'
[2025-10-07 12:15:50] جاري تحميل خيارات reservationRequest...
[2025-10-07 12:16:05] ❌ فشل تحميل reservationRequest: 422 - {"code":"VALIDATION_ERROR","message":"Missing or invalid hookId","details":[],"statusCode":422,"supportId":"40eb2150-a35e-11f0-9542-318ec91b7e92"}
[2025-10-07 12:16:05] ✅ تم تحميل 34 خدمة
[2025-10-07 12:16:21] جاري تسجيل الدخول...
[2025-10-07 12:16:21] ✅ تم تسجيل الدخول بنجاح
[2025-10-07 12:16:21] جاري تحميل الخدمات...
[2025-10-07 12:16:22] ❌ لا توجد بيانات للخدمة المحددة
[2025-10-07 12:16:22] 🔄 تم اختيار الخدمة: بيتك فى مصر
[2025-10-07 12:16:22] جاري تحميل خيارات الاستكمال للخدمة: bookunit
[2025-10-07 12:30:19] ❌ خطأ في تحميل خيارات الاستكمال: 'selectUnit'
[2025-10-07 12:30:19] جاري تحميل خيارات reservationRequest...
[2025-10-07 12:31:10] ❌ فشل تحميل reservationRequest: 401 - {"code":"AUTHORIZATION_REQUIRED","message":"Authorization Required","details":[],"statusCode":401,"supportId":"5c3aa320-a360-11f0-8ff1-7f038866751e"}
[2025-10-07 12:31:10] ✅ تم تحميل 34 خدمة
[2025-10-07 12:31:13] 🔄 تم اختيار الخدمة: فالى تاورز ايست
[2025-10-07 12:31:13] جاري تحميل خيارات الاستكمال للخدمة: VALLEYTAWERSEAST
[2025-10-07 12:31:13] ❌ فشل تحميل خيارات الاستكمال: 403
[2025-10-07 12:31:13] جاري تحميل خيارات reservationRequest...
[2025-10-07 12:31:17] ❌ فشل تحميل reservationRequest: 401 - {"code":"AUTHORIZATION_REQUIRED","message":"Authorization Required","details":[],"statusCode":401,"supportId":"60569400-a360-11f0-8571-37c4161f45bc"}
[2025-10-07 12:31:26] 🔄 تم اختيار الخدمة: ديارنا - 15 مايو
[2025-10-07 12:31:26] جاري تحميل خيارات الاستكمال للخدمة: DIARNAFIFTEENMAY
[2025-10-07 12:31:27] ❌ فشل تحميل خيارات الاستكمال: 403
[2025-10-07 12:31:27] جاري تحميل خيارات reservationRequest...
[2025-10-07 12:31:28] ❌ فشل تحميل reservationRequest: 401 - {"code":"AUTHORIZATION_REQUIRED","message":"Authorization Required","details":[],"statusCode":401,"supportId":"67294070-a360-11f0-b284-3184a3863547"}
[2025-10-07 12:31:38] 🔄 تم اختيار الخدمة: دار مصر - بدر
[2025-10-07 12:31:38] جاري تحميل خيارات الاستكمال للخدمة: DARMISRBADR
[2025-10-07 12:31:39] ❌ فشل تحميل خيارات الاستكمال: 403
[2025-10-07 12:31:42] 🔄 تم اختيار الخدمة: ديارنا - 15 مايو
[2025-10-07 12:31:42] جاري تحميل خيارات الاستكمال للخدمة: DIARNAFIFTEENMAY
[2025-10-07 12:31:43] ❌ فشل تحميل خيارات الاستكمال: 403
[2025-10-07 12:31:47] 🔄 تم اختيار الخدمة: دار مصر - بدر
[2025-10-07 12:31:47] جاري تحميل خيارات الاستكمال للخدمة: DARMISRBADR
[2025-10-07 12:31:47] ❌ فشل تحميل خيارات الاستكمال: 403
[2025-10-07 12:31:47] جاري تحميل خيارات reservationRequest...
[2025-10-07 12:32:14] ❌ فشل تحميل reservationRequest: 401 - {"code":"AUTHORIZATION_REQUIRED","message":"Authorization Required","details":[],"statusCode":401,"supportId":"79d55f60-a360-11f0-a443-c57b22475421"}
[2025-10-07 12:32:18] جاري تسجيل الدخول...
[2025-10-07 12:32:19] ✅ تم تسجيل الدخول بنجاح
[2025-10-07 12:32:19] جاري تحميل الخدمات...
[2025-10-07 12:32:20] ❌ لا توجد بيانات للخدمة المحددة
[2025-10-07 12:32:20] 🔄 تم اختيار الخدمة: بيتك فى مصر
[2025-10-07 12:32:20] جاري تحميل خيارات الاستكمال للخدمة: bookunit
[2025-10-07 12:32:23] ❌ خطأ في تحميل خيارات الاستكمال: 'selectUnit'
[2025-10-07 12:32:23] جاري تحميل خيارات reservationRequest...
[2025-10-07 12:32:40] ❌ فشل تحميل reservationRequest: 422 - {"code":"VALIDATION_ERROR","message":"Missing or invalid hookId","details":[],"statusCode":422,"supportId":"87caad00-a360-11f0-b1fa-93936deffd83"}
[2025-10-07 12:32:40] ✅ تم تحميل 34 خدمة
[2025-10-07 12:32:46] 🔄 تم اختيار الخدمة: ديارنا - 15 مايو
[2025-10-07 12:32:46] جاري تحميل خيارات الاستكمال للخدمة: DIARNAFIFTEENMAY
[2025-10-07 12:32:47] ✅ تم تحميل 4 خيار للاستكمال
[2025-10-07 12:32:47] جاري تحميل خيارات reservationRequest...
[2025-10-07 12:32:53] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 12:32:56] جاري تحميل خيارات model...
[2025-10-07 12:33:01] ✅ تم تحميل 2 خيار لـ model
[2025-10-07 12:33:03] جاري تحميل خيارات buildingNumber...
[2025-10-07 12:33:04] ✅ تم تحميل 13 خيار لـ buildingNumber
[2025-10-07 12:33:07] جاري تحميل خيارات floor...
[2025-10-07 12:33:07] ✅ تم تحميل 7 خيار لـ floor
[2025-10-07 12:33:39] جاري تحميل خيارات buildingNumber...
[2025-10-07 13:02:00] جاري تسجيل الدخول...
[2025-10-07 13:02:01] ✅ تم تسجيل الدخول بنجاح
[2025-10-07 13:02:01] جاري تحميل الخدمات...
[2025-10-07 13:02:02] 🔄 تم اختيار الخدمة: بيتك فى مصر
[2025-10-07 13:02:02] جاري تحميل خيارات الاستكمال للخدمة: bookunit
[2025-10-07 13:02:05] ❌ خطأ في تحميل خيارات الاستكمال: 'selectUnit'
[2025-10-07 13:02:05] جاري تحميل خيارات reservationRequest...
[2025-10-07 13:02:06] ❌ فشل تحميل reservationRequest: 422 - {"code":"VALIDATION_ERROR","message":"Missing or invalid hookId","details":[],"statusCode":422,"supportId":"adf929d0-a364-11f0-b1fa-93936deffd83"}
[2025-10-07 13:02:06] ✅ تم تحميل 34 خدمة
[2025-10-07 13:02:20] 🔄 تم اختيار الخدمة: ديارنا - 15 مايو
[2025-10-07 13:02:20] جاري تحميل خيارات الاستكمال للخدمة: DIARNAFIFTEENMAY
[2025-10-07 13:02:21] ✅ تم تحميل 4 خيار للاستكمال
[2025-10-07 13:02:21] جاري تحميل خيارات reservationRequest...
[2025-10-07 13:02:23] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 13:02:26] جاري تحميل خيارات model...
[2025-10-07 13:02:27] ✅ تم تحميل 2 خيار لـ model
[2025-10-07 13:02:28] جاري تحميل خيارات buildingNumber...
[2025-10-07 13:02:29] ✅ تم تحميل 6 خيار لـ buildingNumber
[2025-10-07 13:02:31] جاري تحميل خيارات floor...
[2025-10-07 13:02:34] ✅ تم تحميل 7 خيار لـ floor
[2025-10-07 13:03:05] جاري تحميل خيارات buildingNumber...
[2025-10-07 13:03:23] ✅ تم تحميل 7 خيار لـ buildingNumber
[2025-10-07 13:03:34] جاري تحميل خيارات floor...
[2025-10-07 13:03:37] ✅ تم تحميل 2 خيار لـ floor
[2025-10-07 14:24:46] جاري تسجيل الدخول...
[2025-10-07 14:24:46] ✅ تم تسجيل الدخول بنجاح
[2025-10-07 14:24:46] جاري تحميل الخدمات...
[2025-10-07 14:24:47] ✅ تم تحميل 34 خدمة
[2025-10-07 14:24:52] 🔄 تم اختيار الخدمة: ديارنا - 15 مايو
[2025-10-07 14:24:52] جاري تحميل خيارات الاستكمال للخدمة: DIARNAFIFTEENMAY
[2025-10-07 14:24:53] ✅ تم تحميل 4 خيار للاستكمال
[2025-10-07 14:24:53] جاري تحميل خيارات reservationRequest...
[2025-10-07 14:24:54] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 14:24:55] جاري تحميل خيارات model...
[2025-10-07 14:24:56] ✅ تم تحميل 2 خيار لـ model
[2025-10-07 14:24:57] جاري تحميل خيارات buildingNumber...
[2025-10-07 14:24:57] ✅ تم تحميل 7 خيار لـ buildingNumber
[2025-10-07 14:24:59] جاري تحميل خيارات floor...
[2025-10-07 14:24:59] ✅ تم تحميل 2 خيار لـ floor
[2025-10-07 14:25:00] جاري تحميل خيارات unitNumber...
[2025-10-07 14:25:01] ✅ تم تحميل 2 خيار لـ unitNumber
[2025-10-07 14:26:36] جاري تسجيل الدخول...
[2025-10-07 14:26:37] ✅ تم تسجيل الدخول بنجاح
[2025-10-07 14:26:37] جاري تحميل الخدمات...
[2025-10-07 14:26:38] ✅ تم تحميل 34 خدمة
[2025-10-07 14:26:41] 🔄 تم اختيار الخدمة: ديارنا - 15 مايو
[2025-10-07 14:26:41] جاري تحميل خيارات الاستكمال للخدمة: DIARNAFIFTEENMAY
[2025-10-07 14:26:42] ✅ تم تحميل 4 خيار للاستكمال
[2025-10-07 14:26:42] جاري تحميل خيارات reservationRequest...
[2025-10-07 14:26:48] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 14:26:51] جاري تحميل خيارات model...
[2025-10-07 14:26:52] ✅ تم تحميل 2 خيار لـ model
[2025-10-07 14:26:53] جاري تحميل خيارات buildingNumber...
[2025-10-07 14:26:53] ✅ تم تحميل 7 خيار لـ buildingNumber
[2025-10-07 14:26:56] جاري تحميل خيارات buildingNumber...
[2025-10-07 14:26:56] ✅ تم تحميل 6 خيار لـ buildingNumber
[2025-10-07 14:26:59] جاري تحميل خيارات buildingNumber...
[2025-10-07 14:27:00] ✅ تم تحميل 7 خيار لـ buildingNumber
[2025-10-07 14:27:03] جاري تحميل خيارات buildingNumber...
[2025-10-07 14:27:03] ✅ تم تحميل 6 خيار لـ buildingNumber
[2025-10-07 14:27:05] جاري تحميل خيارات floor...
[2025-10-07 14:27:05] ✅ تم تحميل 7 خيار لـ floor
[2025-10-07 14:27:08] جاري تحميل خيارات unitNumber...
[2025-10-07 14:27:08] ✅ تم تحميل 4 خيار لـ unitNumber
[2025-10-07 14:27:42] جاري تحميل خيارات model...
[2025-10-07 14:27:43] ✅ تم تحميل 2 خيار لـ model
[2025-10-07 14:27:44] جاري تحميل خيارات buildingNumber...
[2025-10-07 14:27:45] ✅ تم تحميل 7 خيار لـ buildingNumber
[2025-10-07 14:27:46] جاري تحميل خيارات floor...
[2025-10-07 14:27:46] ✅ تم تحميل 7 خيار لـ floor
[2025-10-07 14:27:47] جاري تحميل خيارات unitNumber...
[2025-10-07 14:27:48] ✅ تم تحميل 4 خيار لـ unitNumber
[2025-10-07 14:27:52] جاري تحميل خيارات model...
[2025-10-07 14:27:53] ✅ تم تحميل 2 خيار لـ model
[2025-10-07 14:27:55] جاري تحميل خيارات buildingNumber...
[2025-10-07 14:27:55] ✅ تم تحميل 7 خيار لـ buildingNumber
[2025-10-07 14:27:56] جاري تحميل خيارات floor...
[2025-10-07 14:27:57] ✅ تم تحميل 7 خيار لـ floor
[2025-10-07 14:27:58] جاري تحميل خيارات unitNumber...
[2025-10-07 14:27:58] ✅ تم تحميل 4 خيار لـ unitNumber
[2025-10-07 14:28:14] ✅ تمت إضافة محاولة: A - 37 - الأرضي - 1 - 10396
[2025-10-07 14:28:15] ✅ تمت إضافة محاولة: A - 37 - الأرضي - 1 - 10396
[2025-10-07 14:28:18] ✅ تم حذف المحاولة
[2025-10-07 14:57:28] جاري تحميل خيارات model...
[2025-10-07 14:57:28] ❌ فشل تحميل model: 401 - {"code":"AUTHORIZATION_REQUIRED","message":"Authorization Required","details":[],"statusCode":401,"supportId":"cc905430-a374-11f0-a1ff-77a857010dce"}
[2025-10-07 14:57:29] جاري تحميل خيارات reservationRequest...
[2025-10-07 14:57:29] ❌ فشل تحميل reservationRequest: 401 - {"code":"AUTHORIZATION_REQUIRED","message":"Authorization Required","details":[],"statusCode":401,"supportId":"cccd0f10-a374-11f0-ba4c-2f77bcdb8930"}
[2025-10-07 14:57:29] جاري تحميل خيارات reservationRequest...
[2025-10-07 14:57:30] ❌ فشل تحميل reservationRequest: 401 - {"code":"AUTHORIZATION_REQUIRED","message":"Authorization Required","details":[],"statusCode":401,"supportId":"cd38c930-a374-11f0-b73c-bbd17605431a"}
[2025-10-07 14:57:32] جاري تحميل خيارات model...
[2025-10-07 14:57:32] ❌ فشل تحميل model: 401 - {"code":"AUTHORIZATION_REQUIRED","message":"Authorization Required","details":[],"statusCode":401,"supportId":"ceae0410-a374-11f0-a6a0-0f5137418bd0"}
[2025-10-07 14:57:32] جاري تحميل خيارات reservationRequest...
[2025-10-07 14:57:32] ❌ فشل تحميل reservationRequest: 401 - {"code":"AUTHORIZATION_REQUIRED","message":"Authorization Required","details":[],"statusCode":401,"supportId":"ced9cf00-a374-11f0-9542-318ec91b7e92"}
[2025-10-07 14:57:36] 🔄 تم اختيار الخدمة: دار مصر - برج العرب
[2025-10-07 14:57:36] جاري تحميل خيارات الاستكمال للخدمة: DARMISRBORGELARAB
[2025-10-07 14:57:36] ❌ فشل تحميل خيارات الاستكمال: 403
[2025-10-07 14:57:36] جاري تحميل خيارات model...
[2025-10-07 14:57:37] ❌ فشل تحميل model: 401 - {"code":"AUTHORIZATION_REQUIRED","message":"Authorization Required","details":[],"statusCode":401,"supportId":"d167cce0-a374-11f0-82b2-afc3c04d7808"}
[2025-10-07 14:57:37] جاري تحميل خيارات reservationRequest...
[2025-10-07 14:57:37] ❌ فشل تحميل reservationRequest: 401 - {"code":"AUTHORIZATION_REQUIRED","message":"Authorization Required","details":[],"statusCode":401,"supportId":"d1a12c60-a374-11f0-801d-3b3cdf89dba7"}
[2025-10-07 14:57:48] ✅ تم حذف المحاولة
[2025-10-07 18:31:52] جاري تسجيل الدخول...
[2025-10-07 18:31:53] ✅ تم تسجيل الدخول بنجاح
[2025-10-07 18:31:53] جاري تحميل الخدمات...
[2025-10-07 18:31:53] ✅ تم تحميل 34 خدمة
[2025-10-07 18:32:00] 🔄 تم اختيار الخدمة: ديارنا - 15 مايو
[2025-10-07 18:32:00] جاري تحميل خيارات الاستكمال للخدمة: DIARNAFIFTEENMAY
[2025-10-07 18:32:00] ✅ تم تحميل 4 خيار للاستكمال
[2025-10-07 18:32:00] جاري تحميل خيارات reservationRequest...
[2025-10-07 18:32:00] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 18:32:03] جاري تحميل خيارات model...
[2025-10-07 18:32:03] 🔓 تحميل جميع البيانات بدون فلتر لـ model
[2025-10-07 18:32:03] ✅ تم تحميل 2 خيار لـ model
[2025-10-07 18:32:03] جاري تحميل خيارات reservationRequest...
[2025-10-07 18:32:03] 🔓 تحميل جميع البيانات بدون فلتر لـ reservationRequest
[2025-10-07 18:32:05] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 18:32:20] 🔄 تم اختيار الخدمة: ديارنا - اسيوط الجديدة
[2025-10-07 18:32:20] جاري تحميل خيارات الاستكمال للخدمة: DIARNANEWASYUT
[2025-10-07 18:32:20] ✅ تم تحميل 4 خيار للاستكمال
[2025-10-07 18:32:20] جاري تحميل خيارات model...
[2025-10-07 18:32:20] 🔓 تحميل جميع البيانات بدون فلتر لـ model
[2025-10-07 18:32:21] ✅ تم تحميل 2 خيار لـ model
[2025-10-07 18:32:21] جاري تحميل خيارات reservationRequest...
[2025-10-07 18:32:21] 🔓 تحميل جميع البيانات بدون فلتر لـ reservationRequest
[2025-10-07 18:32:21] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 18:32:24] 🔄 تم اختيار الخدمة: ديارنا - 15 مايو
[2025-10-07 18:32:24] جاري تحميل خيارات الاستكمال للخدمة: DIARNAFIFTEENMAY
[2025-10-07 18:32:24] ✅ تم تحميل 4 خيار للاستكمال
[2025-10-07 18:32:24] جاري تحميل خيارات model...
[2025-10-07 18:32:24] 🔓 تحميل جميع البيانات بدون فلتر لـ model
[2025-10-07 18:32:25] ✅ تم تحميل 2 خيار لـ model
[2025-10-07 18:32:25] جاري تحميل خيارات reservationRequest...
[2025-10-07 18:32:25] 🔓 تحميل جميع البيانات بدون فلتر لـ reservationRequest
[2025-10-07 18:32:26] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 18:32:28] جاري تحميل خيارات buildingNumber...
[2025-10-07 18:32:28] 🔓 تحميل جميع البيانات بدون فلتر لـ buildingNumber
[2025-10-07 18:32:31] ✅ تم تحميل 13 خيار لـ buildingNumber
[2025-10-07 18:32:34] جاري تحميل خيارات floor...
[2025-10-07 18:32:34] 🔓 تحميل جميع البيانات بدون فلتر لـ floor
[2025-10-07 18:32:34] ✅ تم تحميل 7 خيار لـ floor
[2025-10-07 18:32:36] جاري تحميل خيارات unitNumber...
[2025-10-07 18:32:36] 🔓 تحميل جميع البيانات بدون فلتر لـ unitNumber
[2025-10-07 18:32:37] ✅ تم تحميل 26 خيار لـ unitNumber
[2025-10-07 18:32:51] جاري تحميل خيارات reservationRequest...
[2025-10-07 18:32:59] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 18:33:03] جاري تحميل خيارات model...
[2025-10-07 18:33:04] ✅ تم تحميل 2 خيار لـ model
[2025-10-07 18:33:07] جاري تحميل خيارات model...
[2025-10-07 18:33:07] 🔓 تحميل جميع البيانات بدون فلتر لـ model
[2025-10-07 18:33:07] ✅ تم تحميل 2 خيار لـ model
[2025-10-07 18:33:07] جاري تحميل خيارات reservationRequest...
[2025-10-07 18:33:07] 🔓 تحميل جميع البيانات بدون فلتر لـ reservationRequest
[2025-10-07 18:33:07] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 18:35:31] 🔓 النظام القديم: تحميل جميع البيانات لجميع الحقول بدون قيود
[2025-10-07 18:35:50] 🔓 النظام القديم: تحميل جميع البيانات لجميع الحقول بدون قيود
[2025-10-07 18:35:55] جاري تسجيل الدخول...
[2025-10-07 18:35:56] ✅ تم تسجيل الدخول بنجاح
[2025-10-07 18:35:56] جاري تحميل الخدمات...
[2025-10-07 18:35:56] ✅ تم تحميل 34 خدمة
[2025-10-07 18:36:00] 🔄 تم اختيار الخدمة: ديارنا - 15 مايو
[2025-10-07 18:36:00] جاري تحميل خيارات الاستكمال للخدمة: DIARNAFIFTEENMAY
[2025-10-07 18:36:01] ✅ تم تحميل 4 خيار للاستكمال
[2025-10-07 18:36:01] 🔓 النظام القديم: تحميل جميع البيانات لجميع الحقول بدون قيود
[2025-10-07 18:36:01] جاري تحميل خيارات reservationRequest...
[2025-10-07 18:36:01] 🔓 تحميل جميع البيانات بدون فلتر لـ reservationRequest
[2025-10-07 18:36:01] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 18:36:01] جاري تحميل خيارات model...
[2025-10-07 18:36:01] 🔓 تحميل جميع البيانات بدون فلتر لـ model
[2025-10-07 18:36:02] ✅ تم تحميل 2 خيار لـ model
[2025-10-07 18:36:02] جاري تحميل خيارات buildingNumber...
[2025-10-07 18:36:02] 🔓 تحميل جميع البيانات بدون فلتر لـ buildingNumber
[2025-10-07 18:36:03] ✅ تم تحميل 13 خيار لـ buildingNumber
[2025-10-07 18:36:03] جاري تحميل خيارات floor...
[2025-10-07 18:36:03] 🔓 تحميل جميع البيانات بدون فلتر لـ floor
[2025-10-07 18:36:03] ✅ تم تحميل 7 خيار لـ floor
[2025-10-07 18:36:03] جاري تحميل خيارات unitNumber...
[2025-10-07 18:36:03] 🔓 تحميل جميع البيانات بدون فلتر لـ unitNumber
[2025-10-07 18:36:04] ✅ تم تحميل 26 خيار لـ unitNumber
[2025-10-07 18:36:12] جاري تحميل خيارات reservationRequest...
[2025-10-07 18:36:13] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 18:36:14] جاري تحميل خيارات model...
[2025-10-07 18:36:15] ✅ تم تحميل 2 خيار لـ model
[2025-10-07 18:36:16] جاري تحميل خيارات buildingNumber...
[2025-10-07 18:36:17] ✅ تم تحميل 7 خيار لـ buildingNumber
[2025-10-07 18:36:18] جاري تحميل خيارات floor...
[2025-10-07 18:36:19] ✅ تم تحميل 7 خيار لـ floor
[2025-10-07 18:36:21] جاري تحميل خيارات unitNumber...
[2025-10-07 18:36:21] ✅ تم تحميل 4 خيار لـ unitNumber
[2025-10-07 18:36:25] ✅ تمت إضافة محاولة: A - 55 - الثاني - 11 - 10396
[2025-10-07 18:36:33] 🔍 جاري اختبار الحصول على تفاصيل الوحدة...
[2025-10-07 18:36:34] ✅ تم الحصول على تفاصيل الوحدة بنجاح
[2025-10-07 18:36:42] 🔓 النظام القديم: تحميل جميع البيانات لجميع الحقول بدون قيود
[2025-10-07 18:36:42] جاري تحميل خيارات reservationRequest...
[2025-10-07 18:36:42] 🔓 تحميل جميع البيانات بدون فلتر لـ reservationRequest
[2025-10-07 18:36:43] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 18:36:43] جاري تحميل خيارات model...
[2025-10-07 18:36:43] 🔓 تحميل جميع البيانات بدون فلتر لـ model
[2025-10-07 18:36:43] ✅ تم تحميل 2 خيار لـ model
[2025-10-07 18:36:43] جاري تحميل خيارات buildingNumber...
[2025-10-07 18:36:43] 🔓 تحميل جميع البيانات بدون فلتر لـ buildingNumber
[2025-10-07 18:36:47] ✅ تم تحميل 13 خيار لـ buildingNumber
[2025-10-07 18:36:47] جاري تحميل خيارات floor...
[2025-10-07 18:36:47] 🔓 تحميل جميع البيانات بدون فلتر لـ floor
[2025-10-07 18:36:50] ✅ تم تحميل 7 خيار لـ floor
[2025-10-07 18:36:50] جاري تحميل خيارات unitNumber...
[2025-10-07 18:36:50] 🔓 تحميل جميع البيانات بدون فلتر لـ unitNumber
[2025-10-07 18:36:51] ✅ تم تحميل 26 خيار لـ unitNumber
[2025-10-07 18:36:58] 🔍 جاري اختبار الحصول على تفاصيل الوحدة...
[2025-10-07 18:36:59] ✅ تم الحصول على تفاصيل الوحدة بنجاح
[2025-10-07 18:37:11] ✅ تم حذف المحاولة
[2025-10-07 18:37:12] ✅ تمت إضافة محاولة: A - 51 - الثاني - 12 - 10396
[2025-10-07 18:37:13] 🔍 جاري اختبار الحصول على تفاصيل الوحدة...
[2025-10-07 18:37:14] ✅ تم الحصول على تفاصيل الوحدة بنجاح
[2025-10-07 18:38:10] 🔍 جاري اختبار الحصول على تفاصيل الوحدة...
[2025-10-07 18:38:10] ✅ تم الحصول على تفاصيل الوحدة بنجاح
[2025-10-07 18:38:23] 🔍 جاري اختبار الحصول على تفاصيل الوحدة...
[2025-10-07 18:38:24] ✅ تم الحصول على تفاصيل الوحدة بنجاح
[2025-10-07 18:38:30] ✅ تم حذف المحاولة
[2025-10-07 18:38:31] 🔍 جاري اختبار الحصول على تفاصيل الوحدة...
[2025-10-07 18:38:32] ✅ تم الحصول على تفاصيل الوحدة بنجاح
[2025-10-07 18:49:47] جاري تسجيل الدخول...
[2025-10-07 18:49:47] ✅ تم تسجيل الدخول بنجاح
[2025-10-07 18:49:47] جاري تحميل الخدمات...
[2025-10-07 18:49:48] ✅ تم تحميل 34 خدمة
[2025-10-07 18:49:51] 🔄 تم اختيار الخدمة: ديارنا - 15 مايو
[2025-10-07 18:49:51] جاري تحميل خيارات الاستكمال للخدمة: DIARNAFIFTEENMAY
[2025-10-07 18:49:52] ✅ تم تحميل 4 خيار للاستكمال
[2025-10-07 18:49:52] جاري تحميل خيارات reservationRequest...
[2025-10-07 18:49:52] ✅ تم تحميل 1 خيار لـ reservationRequest
[2025-10-07 18:49:54] جاري تحميل خيارات model...
[2025-10-07 18:49:54] ✅ تم تحميل 2 خيار لـ model
[2025-10-07 18:49:55] جاري تحميل خيارات buildingNumber...
[2025-10-07 18:49:56] ✅ تم تحميل 7 خيار لـ buildingNumber
[2025-10-07 18:49:57] جاري تحميل خيارات floor...
[2025-10-07 18:49:58] ✅ تم تحميل 7 خيار لـ floor
[2025-10-07 18:49:59] جاري تحميل خيارات unitNumber...
[2025-10-07 18:50:00] ✅ تم تحميل 4 خيار لـ unitNumber
[2025-10-07 18:50:03] ✅ تمت إضافة محاولة: A - 50 - الأرضي - 1 - 10396
[2025-10-07 18:50:06] ✅ تمت إضافة محاولة: A - 50 - الأرضي - 2 - 10396
